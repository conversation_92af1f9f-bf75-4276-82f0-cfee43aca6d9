import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sys

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات المحلية
import config
import utils
from ui.personal_info import PersonalInfoFrame
from ui.project_info import ProjectInfoFrame
from ui.market_analysis import MarketAnalysisFrame
from ui.swot import SWOTFrame
from ui.marketing_mix import MarketingMixFrame
from ui.production import ProductionFrame
from ui.financials import FinancialsFrame
from ui.summary import SummaryFrame
from data.models import ProjectData
from data.data_manager import DataManager
from data.export_manager import ExportManager

SECTIONS = [
    ("المعلومات الشخصية", PersonalInfoFrame),
    ("بيانات المشروع", ProjectInfoFrame),
    ("دراسة السوق والمنافسين", MarketAnalysisFrame),
    ("تحليل SWOT", SWOTFrame),
    ("المزيج التسويقي", MarketingMixFrame),
    ("مستلزمات الإنتاج", ProductionFrame),
    ("الدراسة المالية", FinancialsFrame),
    ("ملخص الربح السنوي", SummaryFrame)
]

class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title(config.get_config("ui", "window_title"))
        self.geometry(config.get_config("ui", "window_size"))
        self.configure(bg=config.get_config("ui", "background_color"))
        self.state('zoomed')  # تكبير النافذة
        
        # بيانات المشروع ومديري البيانات
        self.project_data = ProjectData()
        self.data_manager = DataManager()
        self.export_manager = ExportManager()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل آخر مشروع محفوظ إن وجد
        self.load_last_project()

        # بدء الحفظ التلقائي
        self.auto_save()

    def setup_ui(self):
        # شريط القوائم العلوي
        self.create_menu_bar()
        
        # العنوان الرئيسي
        header_frame = tk.Frame(self, bg="#f2f4f8")
        header_frame.pack(fill="x", pady=10)
        
        header = tk.Label(header_frame, text="نظام دراسة جدوى شامل", 
                         font=("Tajawal", 24, "bold"), bg="#f2f4f8", fg="#173360")
        header.pack()
        
        # شريط الأدوات
        self.create_toolbar(header_frame)

        # إطار رئيسي مقسم: قائمة أقسام يمين + محتوى يسار
        main_frame = tk.Frame(self, bg="#f2f4f8")
        main_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # قائمة أقسام
        self.create_sidebar(main_frame)

        # منطقة المحتوى
        self.content_frame = tk.Frame(main_frame, bg="#ffffff", relief="raised", bd=1)
        self.content_frame.pack(side="left", fill="both", expand=True, padx=(0, 10))

        # تحميل الأقسام
        self.load_sections()

    def create_menu_bar(self):
        menubar = tk.Menu(self)
        self.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="مشروع جديد", command=self.new_project)
        file_menu.add_command(label="فتح مشروع", command=self.open_project)
        file_menu.add_command(label="حفظ المشروع", command=self.save_project)
        file_menu.add_command(label="حفظ باسم", command=self.save_project_as)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير إلى PDF", command=self.export_to_pdf)
        file_menu.add_command(label="تصدير إلى Word", command=self.export_to_word)
        file_menu.add_command(label="تصدير إلى Excel", command=self.export_to_excel)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.quit)

        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="التحقق من البيانات", command=self.validate_all_data)
        tools_menu.add_command(label="إحصائيات المشروع", command=self.show_project_statistics)
        tools_menu.add_separator()
        tools_menu.add_command(label="حفظ تلقائي الآن", command=lambda: self.auto_save())

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)

    def create_toolbar(self, parent):
        toolbar = tk.Frame(parent, bg="#f2f4f8")
        toolbar.pack(pady=10)
        
        # أزرار الأدوات
        btn_style = {"font": ("Tajawal", 11), "bg": "#4a90e2", "fg": "white", 
                    "relief": "flat", "padx": 15, "pady": 5}
        
        tk.Button(toolbar, text="💾 حفظ", command=self.save_project, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="📁 فتح", command=self.open_project, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="📄 جديد", command=self.new_project, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="📊 تصدير PDF", command=self.export_to_pdf, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="✅ تحقق من البيانات", command=self.validate_all_data, **btn_style).pack(side="right", padx=5)

    def create_sidebar(self, parent):
        sidebar = tk.Frame(parent, bg="#e7eefa", width=250, relief="raised", bd=1)
        sidebar.pack(side="right", fill="y")
        sidebar.pack_propagate(0)
        
        # عنوان القائمة الجانبية
        tk.Label(sidebar, text="أقسام دراسة الجدوى", font=("Tajawal", 14, "bold"), 
                bg="#e7eefa", fg="#173360").pack(pady=(20,15))
        
        # أزرار الأقسام
        self.section_buttons = {}
        for idx, (section, section_class) in enumerate(SECTIONS):
            btn = tk.Button(sidebar, text=f"{idx+1}. {section}", 
                           font=("Tajawal", 12), anchor="e",
                           bg="#ecf2fa", fg="#17486d", relief="flat", 
                           padx=15, pady=10, width=25,
                           command=lambda s=section: self.show_section(s))
            btn.pack(fill="x", pady=3, padx=10)
            self.section_buttons[section] = btn

    def load_sections(self):
        # إنشاء كل إطار مرة واحدة
        self.section_frames = {}
        self.current_section = None

        for section, section_class in SECTIONS:
            frame = section_class(self.content_frame, self.project_data)
            self.section_frames[section] = frame

        # عرض القسم الأول افتراضياً
        self.show_section(SECTIONS[0][0])

    def show_section(self, section_name):
        # إخفاء القسم الحالي
        if self.current_section:
            self.section_frames[self.current_section].pack_forget()
            self.section_buttons[self.current_section].config(bg="#ecf2fa")
        
        # عرض القسم الجديد
        self.section_frames[section_name].pack(fill="both", expand=True, padx=20, pady=20)
        self.section_buttons[section_name].config(bg="#4a90e2", fg="white")
        self.current_section = section_name

    def new_project(self):
        if messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟ سيتم فقدان البيانات غير المحفوظة."):
            self.project_data = ProjectData()
            for frame in self.section_frames.values():
                frame.clear_data()
            messagebox.showinfo("نجح", "تم إنشاء مشروع جديد")

    def save_project(self):
        if not hasattr(self, 'current_file'):
            self.save_project_as()
        else:
            self.save_to_file(self.current_file)

    def save_project_as(self):
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")],
            title="حفظ المشروع"
        )
        if filename:
            self.save_to_file(filename)
            self.current_file = filename

    def save_to_file(self, filename):
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()
            
            # حفظ البيانات
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.project_data.to_dict(), f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المشروع: {str(e)}")

    def open_project(self):
        filename = filedialog.askopenfilename(
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")],
            title="فتح مشروع"
        )
        if filename:
            self.load_from_file(filename)
            self.current_file = filename

    def load_from_file(self, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.project_data.from_dict(data)
            
            # تحديث جميع الأقسام
            for frame in self.section_frames.values():
                frame.load_data()
            
            messagebox.showinfo("نجح", "تم تحميل المشروع بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشروع: {str(e)}")

    def load_last_project(self):
        # محاولة تحميل آخر مشروع محفوظ
        if os.path.exists("last_project.json"):
            try:
                self.load_from_file("last_project.json")
            except:
                pass

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # تصدير إلى PDF
            filepath = self.export_manager.export_to_pdf(self.project_data)
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF:\n{str(e)}")

    def export_to_word(self):
        """تصدير إلى Word"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # تصدير إلى Word
            filepath = self.export_manager.export_to_word(self.project_data)
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Word:\n{str(e)}")

    def export_to_excel(self):
        """تصدير البيانات المالية إلى Excel"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # تصدير إلى Excel
            filepath = self.export_manager.export_to_excel(self.project_data)
            messagebox.showinfo("نجح", f"تم تصدير البيانات المالية بنجاح إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Excel:\n{str(e)}")

    def validate_all_data(self):
        """التحقق من صحة جميع البيانات"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # التحقق من صحة البيانات
            errors = self.project_data.validate_data()

            if errors:
                error_message = "تم العثور على الأخطاء التالية:\n\n"
                for i, error in enumerate(errors, 1):
                    error_message += f"{i}. {error}\n"

                messagebox.showwarning("تحذير - بيانات ناقصة", error_message)
            else:
                # عرض إحصائيات المشروع
                stats = self.project_data.get_summary_statistics()

                stats_message = "✅ جميع البيانات صحيحة!\n\n"
                stats_message += "إحصائيات المشروع:\n"
                stats_message += f"• نسبة الاكتمال: {stats.get('completion_rate', 0):.1f}%\n"
                stats_message += f"• الأقسام المكتملة: {stats.get('completed_sections', 0)} من {stats.get('total_sections', 0)}\n"

                if 'total_investment' in stats:
                    stats_message += f"• إجمالي الاستثمار: {stats['total_investment']:,.2f} ريال\n"

                if 'monthly_operating_cost' in stats:
                    stats_message += f"• التكلفة التشغيلية الشهرية: {stats['monthly_operating_cost']:,.2f} ريال\n"

                messagebox.showinfo("تحقق من البيانات", stats_message)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التحقق من البيانات:\n{str(e)}")

    def show_project_statistics(self):
        """عرض إحصائيات المشروع في نافذة منفصلة"""
        try:
            # جمع البيانات
            for frame in self.section_frames.values():
                frame.save_data()

            stats = self.project_data.get_summary_statistics()

            # إنشاء نافذة الإحصائيات
            stats_window = tk.Toplevel(self)
            stats_window.title("إحصائيات المشروع")
            stats_window.geometry("500x400")
            stats_window.configure(bg="#ffffff")

            tk.Label(stats_window, text="إحصائيات المشروع",
                    font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)

            # إطار الإحصائيات
            stats_frame = tk.Frame(stats_window, bg="#f8f9fa", relief="solid", bd=1)
            stats_frame.pack(fill="both", expand=True, padx=20, pady=10)

            # عرض الإحصائيات
            stats_data = [
                ("نسبة اكتمال المشروع", f"{stats.get('completion_rate', 0):.1f}%"),
                ("الأقسام المكتملة", f"{stats.get('completed_sections', 0)} من {stats.get('total_sections', 0)}"),
                ("عدد المعدات", str(stats.get('equipment_count', 0))),
                ("عدد المواد الخام", str(stats.get('raw_materials_count', 0)))
            ]

            if 'total_investment' in stats:
                stats_data.append(("إجمالي الاستثمار المطلوب", f"{stats['total_investment']:,.2f} ريال"))

            if 'monthly_operating_cost' in stats:
                stats_data.append(("التكلفة التشغيلية الشهرية", f"{stats['monthly_operating_cost']:,.2f} ريال"))
                stats_data.append(("التكلفة التشغيلية السنوية", f"{stats['annual_operating_cost']:,.2f} ريال"))

            for i, (label, value) in enumerate(stats_data):
                row_frame = tk.Frame(stats_frame, bg="#f8f9fa")
                row_frame.pack(fill="x", padx=15, pady=5)

                tk.Label(row_frame, text=f"{label}:", font=("Tajawal", 11),
                        bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)

                tk.Label(row_frame, text=value, font=("Tajawal", 11, "bold"),
                        bg="#f8f9fa", fg="#27ae60").pack(side="right")

            # زر الإغلاق
            tk.Button(stats_window, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                     command=stats_window.destroy, padx=20, pady=5).pack(pady=20)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الإحصائيات:\n{str(e)}")

    def auto_save(self):
        """حفظ تلقائي كل 5 دقائق"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # حفظ تلقائي
            with open("auto_save.json", 'w', encoding='utf-8') as f:
                json.dump(self.project_data.to_dict(), f, ensure_ascii=False, indent=2)

        except Exception:
            pass  # تجاهل أخطاء الحفظ التلقائي

        # جدولة الحفظ التلقائي التالي (5 دقائق)
        self.after(300000, self.auto_save)  # 300000 مللي ثانية = 5 دقائق

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
نظام دراسة الجدوى الشامل
الإصدار 1.0

برنامج شامل لإعداد دراسات الجدوى الاقتصادية
يتضمن جميع الأقسام المطلوبة لدراسة جدوى احترافية

الميزات:
• واجهة عربية سهلة الاستخدام
• 8 أقسام شاملة لدراسة الجدوى
• حفظ وتحميل المشاريع
• تصدير إلى PDF وWord وExcel
• حسابات مالية تلقائية
• تحليل SWOT والمزيج التسويقي
• حفظ تلقائي كل 5 دقائق

تطوير: نظام دراسة الجدوى
        """

        about_window = tk.Toplevel(self)
        about_window.title("حول البرنامج")
        about_window.geometry("500x400")
        about_window.configure(bg="#ffffff")
        about_window.resizable(False, False)

        tk.Label(about_window, text="حول البرنامج",
                font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        text_widget = tk.Text(about_window, font=("Tajawal", 11), wrap="word",
                            relief="flat", bg="#f8f9fa", height=15)
        text_widget.pack(fill="both", expand=True, padx=20, pady=10)
        text_widget.insert("1.0", about_text.strip())
        text_widget.config(state="disabled")

        tk.Button(about_window, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=about_window.destroy, padx=20, pady=5).pack(pady=10)

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
دليل الاستخدام السريع

1. البدء:
   • اختر "مشروع جديد" من قائمة ملف
   • أو افتح مشروع موجود

2. إدخال البيانات:
   • انقر على الأقسام في القائمة الجانبية
   • املأ البيانات المطلوبة في كل قسم
   • استخدم زر "حفظ البيانات" في كل قسم

3. الحفظ والتحميل:
   • احفظ مشروعك من قائمة "ملف"
   • يتم الحفظ التلقائي كل 5 دقائق

4. التصدير:
   • صدّر تقريرك إلى PDF أو Word
   • صدّر البيانات المالية إلى Excel

5. التحقق من البيانات:
   • استخدم "التحقق من البيانات" للتأكد من اكتمال المعلومات
   • اعرض إحصائيات المشروع من قائمة "أدوات"

6. الأقسام الرئيسية:
   • المعلومات الشخصية: بيانات صاحب المشروع
   • بيانات المشروع: معلومات أساسية عن المشروع
   • دراسة السوق: تحليل السوق والمنافسين
   • تحليل SWOT: نقاط القوة والضعف والفرص والتهديدات
   • المزيج التسويقي: استراتيجية التسويق
   • مستلزمات الإنتاج: المعدات والمواد الخام
   • الدراسة المالية: التكاليف والإيرادات
   • ملخص الأرباح: النتائج النهائية والتوصيات

نصائح:
• احفظ عملك بانتظام
• تأكد من إدخال جميع البيانات المطلوبة
• استخدم ميزة التحقق من البيانات قبل التصدير
• راجع الحسابات المالية بعناية
        """

        help_window = tk.Toplevel(self)
        help_window.title("دليل الاستخدام")
        help_window.geometry("600x500")
        help_window.configure(bg="#ffffff")

        tk.Label(help_window, text="دليل الاستخدام",
                font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        # إطار النص مع شريط التمرير
        text_frame = tk.Frame(help_window, bg="#ffffff")
        text_frame.pack(fill="both", expand=True, padx=20, pady=10)

        text_widget = tk.Text(text_frame, font=("Tajawal", 10), wrap="word",
                            relief="solid", bd=1)
        text_widget.pack(side="right", fill="both", expand=True)

        scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")

        text_widget.insert("1.0", help_text.strip())
        text_widget.config(state="disabled")

        tk.Button(help_window, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=help_window.destroy, padx=20, pady=5).pack(pady=10)

    def on_closing(self):
        # حفظ تلقائي عند الإغلاق
        try:
            for frame in self.section_frames.values():
                frame.save_data()
            with open("last_project.json", 'w', encoding='utf-8') as f:
                json.dump(self.project_data.to_dict(), f, ensure_ascii=False, indent=2)
        except:
            pass
        self.destroy()

if __name__ == "__main__":
    app = MainApp()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()
