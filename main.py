import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sys

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات المحلية
import config
import utils
import modern_styles
from ui.personal_info import PersonalInfoFrame
from ui.project_info import ProjectInfoFrame
from ui.market_analysis import MarketAnalysisFrame
from ui.swot import SWOTFrame
from ui.marketing_mix import MarketingMixFrame
from ui.production import ProductionFrame
from ui.financials import FinancialsFrame
from ui.summary import SummaryFrame
from data.models import ProjectData
from data.data_manager import DataManager
from data.export_manager import ExportManager

SECTIONS = [
    ("المعلومات الشخصية", PersonalInfoFrame),
    ("بيانات المشروع", ProjectInfoFrame),
    ("دراسة السوق والمنافسين", MarketAnalysisFrame),
    ("تحليل SWOT", SWOTFrame),
    ("المزيج التسويقي", MarketingMixFrame),
    ("مستلزمات الإنتاج", ProductionFrame),
    ("الدراسة المالية", FinancialsFrame),
    ("ملخص الربح السنوي", SummaryFrame)
]

class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title(config.get_config("ui", "window_title"))
        self.geometry(config.get_config("ui", "window_size"))
        self.configure(bg=config.get_config("ui", "background_color"))
        self.state('zoomed')  # تكبير النافذة
        
        # بيانات المشروع ومديري البيانات
        self.project_data = ProjectData()
        self.data_manager = DataManager()
        self.export_manager = ExportManager()
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل آخر مشروع محفوظ إن وجد
        self.load_last_project()

        # بدء الحفظ التلقائي
        self.auto_save()

    def setup_ui(self):
        """إعداد واجهة المستخدم الحديثة"""
        # شريط القوائم العلوي
        self.create_menu_bar()

        # إنشاء الخلفية المتدرجة الحديثة
        self.create_gradient_background()

        # إنشاء قسم Hero الترحيبي
        self.create_hero_section()

        # إنشاء لوحة المعلومات المحسنة
        self.create_dashboard()

        # إطار رئيسي مقسم مع تصميم حديث
        main_frame = tk.Frame(self, bg="#f8fafc")
        main_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # قائمة أقسام حديثة
        self.create_modern_sidebar(main_frame)

        # منطقة المحتوى مع تأثيرات حديثة
        self.content_frame = tk.Frame(main_frame, bg="#ffffff", relief="flat", bd=0)
        self.content_frame.pack(side="left", fill="both", expand=True, padx=(20, 0))

        # إضافة تأثير الظل
        self.add_shadow_effect(self.content_frame)

        # تأكد من أن content_frame متاح للأقسام
        self.content_area = self.content_frame

        # تحميل الأقسام
        self.load_sections()

        # إضافة متغيرات للمؤشر الدائري
        self.progress_arc = None
        self.progress_text = None

    def create_gradient_background(self):
        """إنشاء خلفية متدرجة حديثة"""
        # تغيير لون الخلفية الرئيسية
        self.configure(bg=modern_styles.get_color("background"))

        # إنشاء إطار للخلفية مع تدرج
        bg_frame = tk.Frame(self, bg="#f8fafc")
        bg_frame.place(x=0, y=0, relwidth=1, relheight=1)
        bg_frame.lower()

    def create_hero_section(self):
        """إنشاء قسم Hero الترحيبي"""
        hero_frame = tk.Frame(self, bg=modern_styles.get_color("background"))
        hero_frame.pack(fill="x", pady=20)

        # العنوان الرئيسي مع تدرج
        title_frame = tk.Frame(hero_frame, bg=modern_styles.get_color("background"))
        title_frame.pack()

        # أيقونة متحركة
        sparkle_label = tk.Label(title_frame, text=modern_styles.get_icon("sparkles"),
                                font=modern_styles.get_font("icon_large"),
                                bg=modern_styles.get_color("background"),
                                fg=modern_styles.get_color("accent"))
        sparkle_label.pack(side="right", padx=10)

        # العنوان الرئيسي
        title = tk.Label(title_frame, text="نظام دراسة الجدوى الشامل",
                        font=modern_styles.get_font("title"),
                        bg=modern_styles.get_color("background"),
                        fg=modern_styles.get_color("primary"))
        title.pack(side="right")

        # العنوان الفرعي
        subtitle = tk.Label(hero_frame, text=f"{modern_styles.get_icon('rocket')} برنامج متطور لإعداد دراسات الجدوى الاقتصادية",
                           font=modern_styles.get_font("subheading"),
                           bg=modern_styles.get_color("background"),
                           fg=modern_styles.get_color("text_secondary"))
        subtitle.pack(pady=10)

        # شارات الإنجاز
        badges_frame = tk.Frame(hero_frame, bg=modern_styles.get_color("background"))
        badges_frame.pack(pady=15)

        badges = [
            (modern_styles.get_icon("chart"), "8 أقسام شاملة", modern_styles.get_color("primary")),
            (modern_styles.get_icon("calculator"), "حسابات تلقائية", modern_styles.get_color("secondary")),
            (modern_styles.get_icon("export"), "تصدير متعدد", modern_styles.get_color("accent")),
            (modern_styles.get_icon("save"), "حفظ آمن", modern_styles.get_color("gradient_purple"))
        ]

        for icon, text, color in badges:
            badge = tk.Frame(badges_frame, bg=color, relief="flat")
            badge.pack(side="right", padx=5)

            badge_label = tk.Label(badge, text=f"{icon} {text}",
                                  font=modern_styles.get_font("small"),
                                  bg=color, fg=modern_styles.get_color("text_white"),
                                  padx=12, pady=6)
            badge_label.pack()

    def create_dashboard(self):
        """إنشاء لوحة المعلومات المحسنة"""
        dashboard_frame = tk.Frame(self, bg="#f8fafc")
        dashboard_frame.pack(fill="x", padx=20, pady=10)

        # عنوان لوحة المعلومات
        dash_title = tk.Label(dashboard_frame, text="📈 لوحة المعلومات",
                             font=("Tajawal", 18, "bold"), bg="#f8fafc", fg="#1e293b")
        dash_title.pack(pady=(0, 15))

        # إطار البطاقات
        cards_frame = tk.Frame(dashboard_frame, bg="#f8fafc")
        cards_frame.pack(fill="x")

        # 8 بطاقات إحصائية
        stats_cards = [
            ("👤", "المعلومات الشخصية", "0%", "#3b82f6"),
            ("🏢", "بيانات المشروع", "0%", "#10b981"),
            ("📊", "دراسة السوق", "0%", "#f59e0b"),
            ("⚡", "تحليل SWOT", "0%", "#ef4444"),
            ("🎯", "المزيج التسويقي", "0%", "#8b5cf6"),
            ("🏭", "مستلزمات الإنتاج", "0%", "#06b6d4"),
            ("💰", "الدراسة المالية", "0%", "#84cc16"),
            ("📋", "ملخص الأرباح", "0%", "#f97316")
        ]

        # إنشاء صفين من البطاقات
        for i in range(0, len(stats_cards), 4):
            row_frame = tk.Frame(cards_frame, bg="#f8fafc")
            row_frame.pack(fill="x", pady=5)

            for j in range(4):
                if i + j < len(stats_cards):
                    icon, title, value, color = stats_cards[i + j]
                    self.create_stat_card(row_frame, icon, title, value, color)

    def create_stat_card(self, parent, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = tk.Frame(parent, bg="white", relief="flat", bd=1)
        card.pack(side="left", fill="both", expand=True, padx=5)

        # إضافة تأثير الظل
        shadow = tk.Frame(parent, bg="#e2e8f0", height=2)
        shadow.place(in_=card, x=2, y=2, relwidth=1, relheight=1)
        shadow.lower()

        # محتوى البطاقة
        content_frame = tk.Frame(card, bg="white")
        content_frame.pack(fill="both", expand=True, padx=15, pady=12)

        # الأيقونة
        icon_frame = tk.Frame(content_frame, bg=color, width=40, height=40)
        icon_frame.pack(anchor="ne")
        icon_frame.pack_propagate(False)

        icon_label = tk.Label(icon_frame, text=icon, font=("Arial", 16),
                             bg=color, fg="white")
        icon_label.pack(expand=True)

        # العنوان
        title_label = tk.Label(content_frame, text=title,
                              font=("Tajawal", 11, "bold"),
                              bg="white", fg="#374151")
        title_label.pack(anchor="w", pady=(10, 5))

        # القيمة
        value_label = tk.Label(content_frame, text=value,
                              font=("Tajawal", 16, "bold"),
                              bg="white", fg=color)
        value_label.pack(anchor="w")

    def create_modern_sidebar(self, parent):
        """إنشاء القائمة الجانبية الحديثة"""
        sidebar = tk.Frame(parent, bg="#ffffff", width=320, relief="flat", bd=0)
        sidebar.pack(side="right", fill="y", padx=(0, 20))
        sidebar.pack_propagate(False)

        # إضافة تأثير الظل للقائمة الجانبية
        self.add_shadow_effect(sidebar)

        # عنوان القائمة الجانبية مع تصميم حديث
        header_frame = tk.Frame(sidebar, bg="#1e40af")
        header_frame.pack(fill="x")

        title_label = tk.Label(header_frame, text="🗂️ أقسام دراسة الجدوى",
                              font=("Tajawal", 16, "bold"),
                              bg="#1e40af", fg="white", pady=20)
        title_label.pack()

        # مؤشر التقدم الدائري
        progress_frame = tk.Frame(sidebar, bg="#ffffff")
        progress_frame.pack(fill="x", pady=20)

        self.create_progress_indicator(progress_frame)

        # حاوية الأقسام
        sections_frame = tk.Frame(sidebar, bg="#ffffff")
        sections_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # أزرار الأقسام الحديثة
        self.section_buttons = {}
        section_colors = [
            "#3b82f6", "#10b981", "#f59e0b", "#ef4444",
            "#8b5cf6", "#06b6d4", "#84cc16", "#f97316"
        ]

        for idx, (section, section_class) in enumerate(SECTIONS):
            color = section_colors[idx % len(section_colors)]
            btn_frame = self.create_modern_section_button(
                sections_frame, section, idx + 1, color
            )
            self.section_buttons[section] = btn_frame

    def create_modern_section_button(self, parent, section, number, color):
        """إنشاء زر قسم حديث"""
        # إطار الزر الرئيسي
        btn_frame = tk.Frame(parent, bg="#f8fafc", relief="flat", bd=1)
        btn_frame.pack(fill="x", pady=8)

        # إطار المحتوى
        content_frame = tk.Frame(btn_frame, bg="#f8fafc")
        content_frame.pack(fill="both", expand=True, padx=15, pady=12)

        # رقم القسم مع خلفية ملونة
        number_frame = tk.Frame(content_frame, bg=color, width=35, height=35)
        number_frame.pack(side="right", padx=(0, 15))
        number_frame.pack_propagate(False)

        number_label = tk.Label(number_frame, text=str(number),
                               font=("Tajawal", 14, "bold"),
                               bg=color, fg="white")
        number_label.pack(expand=True)

        # نص القسم
        text_frame = tk.Frame(content_frame, bg="#f8fafc")
        text_frame.pack(side="right", fill="both", expand=True)

        section_label = tk.Label(text_frame, text=section,
                                font=("Tajawal", 12, "bold"),
                                bg="#f8fafc", fg="#374151", anchor="e")
        section_label.pack(anchor="e")

        # مؤشر الحالة
        status_label = tk.Label(text_frame, text="⏳ لم يكتمل",
                               font=("Tajawal", 10),
                               bg="#f8fafc", fg="#6b7280", anchor="e")
        status_label.pack(anchor="e", pady=(2, 0))

        # ربط الأحداث
        def on_click(event=None):
            self.show_section(section)

        def on_enter(event):
            btn_frame.configure(bg="#e0e7ff")
            content_frame.configure(bg="#e0e7ff")
            text_frame.configure(bg="#e0e7ff")
            section_label.configure(bg="#e0e7ff")
            status_label.configure(bg="#e0e7ff")

        def on_leave(event):
            btn_frame.configure(bg="#f8fafc")
            content_frame.configure(bg="#f8fafc")
            text_frame.configure(bg="#f8fafc")
            section_label.configure(bg="#f8fafc")
            status_label.configure(bg="#f8fafc")

        # ربط الأحداث بجميع العناصر
        for widget in [btn_frame, content_frame, text_frame, section_label, status_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)

        return btn_frame

    def create_progress_indicator(self, parent):
        """إنشاء مؤشر التقدم الدائري"""
        progress_frame = tk.Frame(parent, bg="#ffffff")
        progress_frame.pack()

        # دائرة التقدم
        canvas = tk.Canvas(progress_frame, width=120, height=120,
                          bg="#ffffff", highlightthickness=0)
        canvas.pack()

        # رسم الدائرة الخلفية
        canvas.create_oval(10, 10, 110, 110, outline="#e5e7eb", width=8)

        # رسم دائرة التقدم (0% في البداية)
        self.progress_arc = canvas.create_arc(10, 10, 110, 110,
                                             start=90, extent=0,
                                             outline="#3b82f6", width=8, style="arc")

        # نص النسبة المئوية
        self.progress_text = canvas.create_text(60, 60, text="0%",
                                               font=("Tajawal", 16, "bold"),
                                               fill="#1e40af")

        # نص الحالة
        status_label = tk.Label(progress_frame, text="🚀 ابدأ رحلتك!",
                               font=("Tajawal", 12),
                               bg="#ffffff", fg="#64748b")
        status_label.pack(pady=(10, 0))

    def add_shadow_effect(self, widget):
        """إضافة تأثير الظل للعنصر"""
        # إنشاء ظل بسيط باستخدام إطار خلفي
        shadow = tk.Frame(widget.master, bg="#e2e8f0", height=2)
        shadow.place(in_=widget, x=3, y=3, relwidth=1, relheight=1)
        shadow.lower()

    def create_menu_bar(self):
        menubar = tk.Menu(self)
        self.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="مشروع جديد", command=self.new_project)
        file_menu.add_command(label="فتح مشروع", command=self.open_project)
        file_menu.add_command(label="حفظ المشروع", command=self.save_project)
        file_menu.add_command(label="حفظ باسم", command=self.save_project_as)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير إلى PDF", command=self.export_to_pdf)
        file_menu.add_command(label="تصدير إلى Word", command=self.export_to_word)
        file_menu.add_command(label="تصدير إلى Excel", command=self.export_to_excel)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.quit)

        # قائمة الأدوات
        tools_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="أدوات", menu=tools_menu)
        tools_menu.add_command(label="التحقق من البيانات", command=self.validate_all_data)
        tools_menu.add_command(label="إحصائيات المشروع", command=self.show_project_statistics)
        tools_menu.add_separator()
        tools_menu.add_command(label="حفظ تلقائي الآن", command=lambda: self.auto_save())

        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
        help_menu.add_command(label="دليل الاستخدام", command=self.show_help)

    def create_toolbar(self, parent):
        toolbar = tk.Frame(parent, bg="#f2f4f8")
        toolbar.pack(pady=10)
        
        # أزرار الأدوات
        btn_style = {"font": ("Tajawal", 11), "bg": "#4a90e2", "fg": "white", 
                    "relief": "flat", "padx": 15, "pady": 5}
        
        tk.Button(toolbar, text="💾 حفظ", command=self.save_project, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="📁 فتح", command=self.open_project, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="📄 جديد", command=self.new_project, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="📊 تصدير PDF", command=self.export_to_pdf, **btn_style).pack(side="right", padx=5)
        tk.Button(toolbar, text="✅ تحقق من البيانات", command=self.validate_all_data, **btn_style).pack(side="right", padx=5)

    def create_sidebar(self, parent):
        sidebar = tk.Frame(parent, bg="#e7eefa", width=250, relief="raised", bd=1)
        sidebar.pack(side="right", fill="y")
        sidebar.pack_propagate(0)
        
        # عنوان القائمة الجانبية
        tk.Label(sidebar, text="أقسام دراسة الجدوى", font=("Tajawal", 14, "bold"), 
                bg="#e7eefa", fg="#173360").pack(pady=(20,15))
        
        # أزرار الأقسام
        self.section_buttons = {}
        for idx, (section, section_class) in enumerate(SECTIONS):
            btn = tk.Button(sidebar, text=f"{idx+1}. {section}", 
                           font=("Tajawal", 12), anchor="e",
                           bg="#ecf2fa", fg="#17486d", relief="flat", 
                           padx=15, pady=10, width=25,
                           command=lambda s=section: self.show_section(s))
            btn.pack(fill="x", pady=3, padx=10)
            self.section_buttons[section] = btn

    def load_sections(self):
        # إنشاء كل إطار مرة واحدة
        self.section_frames = {}
        self.current_section = None

        for section, section_class in SECTIONS:
            frame = section_class(self.content_frame, self.project_data)
            self.section_frames[section] = frame

        # عرض القسم الأول افتراضياً
        self.show_section(SECTIONS[0][0])

    def show_section(self, section_name):
        """عرض قسم معين مع تحديث التصميم الحديث"""
        # إخفاء القسم الحالي
        if hasattr(self, 'current_section') and self.current_section:
            if hasattr(self, 'section_frames') and self.current_section in self.section_frames:
                self.section_frames[self.current_section].pack_forget()

            # إعادة تعيين لون الزر القديم
            if hasattr(self, 'section_buttons') and self.current_section in self.section_buttons:
                old_btn = self.section_buttons[self.current_section]
                old_btn.configure(bg="#f8fafc")

        # عرض القسم الجديد
        if hasattr(self, 'section_frames') and section_name in self.section_frames:
            self.section_frames[section_name].pack(fill="both", expand=True, padx=25, pady=25)

        # تحديث لون الزر الحالي
        if hasattr(self, 'section_buttons') and section_name in self.section_buttons:
            current_btn = self.section_buttons[section_name]
            current_btn.configure(bg="#e0e7ff")

        self.current_section = section_name

        # تحديث مؤشر التقدم
        self.update_progress_indicator()

    def update_progress_indicator(self):
        """تحديث مؤشر التقدم الدائري"""
        if not hasattr(self, 'progress_arc'):
            return

        # حساب نسبة الإكمال
        completed_sections = 0
        total_sections = len(SECTIONS)

        # فحص كل قسم للتحقق من الإكمال
        for section, _ in SECTIONS:
            if self.is_section_completed(section):
                completed_sections += 1

        # حساب النسبة المئوية
        progress_percentage = (completed_sections / total_sections) * 100

        # تحديث الدائرة (360 درجة = 100%)
        extent = (progress_percentage / 100) * 360

        # تحديث الرسم
        canvas = self.progress_arc.master if hasattr(self.progress_arc, 'master') else None
        if canvas:
            canvas.itemconfig(self.progress_arc, extent=extent)
            canvas.itemconfig(self.progress_text, text=f"{int(progress_percentage)}%")

        # تحديث رسالة الحالة
        if progress_percentage == 0:
            status_msg = "🚀 ابدأ رحلتك!"
        elif progress_percentage < 25:
            status_msg = "💪 استمر في التقدم!"
        elif progress_percentage < 50:
            status_msg = "🔥 أداء رائع!"
        elif progress_percentage < 75:
            status_msg = "⭐ تقدم ممتاز!"
        elif progress_percentage < 100:
            status_msg = "🎯 أوشكت على الانتهاء!"
        else:
            status_msg = "🎉 مبروك! اكتملت!"

        # تحديث نص الحالة إذا كان موجوداً
        # (سيتم تطبيقه لاحقاً عند إنشاء العنصر)

    def is_section_completed(self, section_name):
        """فحص ما إذا كان القسم مكتملاً"""
        # هذه دالة بسيطة - يمكن تطويرها لاحقاً
        if section_name == "المعلومات الشخصية":
            return bool(self.project_data.personal_info.get("اسم صاحب المشروع"))
        elif section_name == "بيانات المشروع":
            return bool(self.project_data.project_info.get("اسم المشروع"))
        elif section_name == "دراسة السوق والمنافسين":
            return bool(self.project_data.market_analysis)
        elif section_name == "تحليل SWOT":
            return any(self.project_data.swot.values())
        elif section_name == "المزيج التسويقي":
            return any(self.project_data.marketing_mix.values())
        elif section_name == "مستلزمات الإنتاج":
            return bool(self.project_data.production.get("equipment") or
                       self.project_data.production.get("raw_materials"))
        elif section_name == "الدراسة المالية":
            return bool(self.project_data.financials)
        elif section_name == "ملخص الربح السنوي":
            return bool(self.project_data.summary)

        return False

    def new_project(self):
        if messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟ سيتم فقدان البيانات غير المحفوظة."):
            self.project_data = ProjectData()
            for frame in self.section_frames.values():
                frame.clear_data()
            messagebox.showinfo("نجح", "تم إنشاء مشروع جديد")

    def save_project(self):
        if not hasattr(self, 'current_file'):
            self.save_project_as()
        else:
            self.save_to_file(self.current_file)

    def save_project_as(self):
        filename = filedialog.asksaveasfilename(
            defaultextension=".json",
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")],
            title="حفظ المشروع"
        )
        if filename:
            self.save_to_file(filename)
            self.current_file = filename

    def save_to_file(self, filename):
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()
            
            # حفظ البيانات
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.project_data.to_dict(), f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("نجح", "تم حفظ المشروع بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المشروع: {str(e)}")

    def open_project(self):
        filename = filedialog.askopenfilename(
            filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")],
            title="فتح مشروع"
        )
        if filename:
            self.load_from_file(filename)
            self.current_file = filename

    def load_from_file(self, filename):
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.project_data.from_dict(data)
            
            # تحديث جميع الأقسام
            for frame in self.section_frames.values():
                frame.load_data()
            
            messagebox.showinfo("نجح", "تم تحميل المشروع بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشروع: {str(e)}")

    def load_last_project(self):
        # محاولة تحميل آخر مشروع محفوظ
        if os.path.exists("last_project.json"):
            try:
                self.load_from_file("last_project.json")
            except:
                pass

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # تصدير إلى PDF
            filepath = self.export_manager.export_to_pdf(self.project_data)
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير PDF:\n{str(e)}")

    def export_to_word(self):
        """تصدير إلى Word"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # تصدير إلى Word
            filepath = self.export_manager.export_to_word(self.project_data)
            messagebox.showinfo("نجح", f"تم تصدير التقرير بنجاح إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Word:\n{str(e)}")

    def export_to_excel(self):
        """تصدير البيانات المالية إلى Excel"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # تصدير إلى Excel
            filepath = self.export_manager.export_to_excel(self.project_data)
            messagebox.showinfo("نجح", f"تم تصدير البيانات المالية بنجاح إلى:\n{filepath}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير Excel:\n{str(e)}")

    def validate_all_data(self):
        """التحقق من صحة جميع البيانات"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # التحقق من صحة البيانات
            errors = self.project_data.validate_data()

            if errors:
                error_message = "تم العثور على الأخطاء التالية:\n\n"
                for i, error in enumerate(errors, 1):
                    error_message += f"{i}. {error}\n"

                messagebox.showwarning("تحذير - بيانات ناقصة", error_message)
            else:
                # عرض إحصائيات المشروع
                stats = self.project_data.get_summary_statistics()

                stats_message = "✅ جميع البيانات صحيحة!\n\n"
                stats_message += "إحصائيات المشروع:\n"
                stats_message += f"• نسبة الاكتمال: {stats.get('completion_rate', 0):.1f}%\n"
                stats_message += f"• الأقسام المكتملة: {stats.get('completed_sections', 0)} من {stats.get('total_sections', 0)}\n"

                if 'total_investment' in stats:
                    stats_message += f"• إجمالي الاستثمار: {stats['total_investment']:,.2f} ريال\n"

                if 'monthly_operating_cost' in stats:
                    stats_message += f"• التكلفة التشغيلية الشهرية: {stats['monthly_operating_cost']:,.2f} ريال\n"

                messagebox.showinfo("تحقق من البيانات", stats_message)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التحقق من البيانات:\n{str(e)}")

    def show_project_statistics(self):
        """عرض إحصائيات المشروع في نافذة منفصلة"""
        try:
            # جمع البيانات
            for frame in self.section_frames.values():
                frame.save_data()

            stats = self.project_data.get_summary_statistics()

            # إنشاء نافذة الإحصائيات
            stats_window = tk.Toplevel(self)
            stats_window.title("إحصائيات المشروع")
            stats_window.geometry("500x400")
            stats_window.configure(bg="#ffffff")

            tk.Label(stats_window, text="إحصائيات المشروع",
                    font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)

            # إطار الإحصائيات
            stats_frame = tk.Frame(stats_window, bg="#f8f9fa", relief="solid", bd=1)
            stats_frame.pack(fill="both", expand=True, padx=20, pady=10)

            # عرض الإحصائيات
            stats_data = [
                ("نسبة اكتمال المشروع", f"{stats.get('completion_rate', 0):.1f}%"),
                ("الأقسام المكتملة", f"{stats.get('completed_sections', 0)} من {stats.get('total_sections', 0)}"),
                ("عدد المعدات", str(stats.get('equipment_count', 0))),
                ("عدد المواد الخام", str(stats.get('raw_materials_count', 0)))
            ]

            if 'total_investment' in stats:
                stats_data.append(("إجمالي الاستثمار المطلوب", f"{stats['total_investment']:,.2f} ريال"))

            if 'monthly_operating_cost' in stats:
                stats_data.append(("التكلفة التشغيلية الشهرية", f"{stats['monthly_operating_cost']:,.2f} ريال"))
                stats_data.append(("التكلفة التشغيلية السنوية", f"{stats['annual_operating_cost']:,.2f} ريال"))

            for i, (label, value) in enumerate(stats_data):
                row_frame = tk.Frame(stats_frame, bg="#f8f9fa")
                row_frame.pack(fill="x", padx=15, pady=5)

                tk.Label(row_frame, text=f"{label}:", font=("Tajawal", 11),
                        bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)

                tk.Label(row_frame, text=value, font=("Tajawal", 11, "bold"),
                        bg="#f8f9fa", fg="#27ae60").pack(side="right")

            # زر الإغلاق
            tk.Button(stats_window, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                     command=stats_window.destroy, padx=20, pady=5).pack(pady=20)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الإحصائيات:\n{str(e)}")

    def auto_save(self):
        """حفظ تلقائي كل 5 دقائق"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                frame.save_data()

            # حفظ تلقائي
            with open("auto_save.json", 'w', encoding='utf-8') as f:
                json.dump(self.project_data.to_dict(), f, ensure_ascii=False, indent=2)

        except Exception:
            pass  # تجاهل أخطاء الحفظ التلقائي

        # جدولة الحفظ التلقائي التالي (5 دقائق)
        self.after(300000, self.auto_save)  # 300000 مللي ثانية = 5 دقائق

    def show_about(self):
        """عرض معلومات حول البرنامج"""
        about_text = """
نظام دراسة الجدوى الشامل
الإصدار 1.0

برنامج شامل لإعداد دراسات الجدوى الاقتصادية
يتضمن جميع الأقسام المطلوبة لدراسة جدوى احترافية

الميزات:
• واجهة عربية سهلة الاستخدام
• 8 أقسام شاملة لدراسة الجدوى
• حفظ وتحميل المشاريع
• تصدير إلى PDF وWord وExcel
• حسابات مالية تلقائية
• تحليل SWOT والمزيج التسويقي
• حفظ تلقائي كل 5 دقائق

تطوير: نظام دراسة الجدوى
        """

        about_window = tk.Toplevel(self)
        about_window.title("حول البرنامج")
        about_window.geometry("500x400")
        about_window.configure(bg="#ffffff")
        about_window.resizable(False, False)

        tk.Label(about_window, text="حول البرنامج",
                font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        text_widget = tk.Text(about_window, font=("Tajawal", 11), wrap="word",
                            relief="flat", bg="#f8f9fa", height=15)
        text_widget.pack(fill="both", expand=True, padx=20, pady=10)
        text_widget.insert("1.0", about_text.strip())
        text_widget.config(state="disabled")

        tk.Button(about_window, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=about_window.destroy, padx=20, pady=5).pack(pady=10)

    def show_help(self):
        """عرض دليل الاستخدام"""
        help_text = """
دليل الاستخدام السريع

1. البدء:
   • اختر "مشروع جديد" من قائمة ملف
   • أو افتح مشروع موجود

2. إدخال البيانات:
   • انقر على الأقسام في القائمة الجانبية
   • املأ البيانات المطلوبة في كل قسم
   • استخدم زر "حفظ البيانات" في كل قسم

3. الحفظ والتحميل:
   • احفظ مشروعك من قائمة "ملف"
   • يتم الحفظ التلقائي كل 5 دقائق

4. التصدير:
   • صدّر تقريرك إلى PDF أو Word
   • صدّر البيانات المالية إلى Excel

5. التحقق من البيانات:
   • استخدم "التحقق من البيانات" للتأكد من اكتمال المعلومات
   • اعرض إحصائيات المشروع من قائمة "أدوات"

6. الأقسام الرئيسية:
   • المعلومات الشخصية: بيانات صاحب المشروع
   • بيانات المشروع: معلومات أساسية عن المشروع
   • دراسة السوق: تحليل السوق والمنافسين
   • تحليل SWOT: نقاط القوة والضعف والفرص والتهديدات
   • المزيج التسويقي: استراتيجية التسويق
   • مستلزمات الإنتاج: المعدات والمواد الخام
   • الدراسة المالية: التكاليف والإيرادات
   • ملخص الأرباح: النتائج النهائية والتوصيات

نصائح:
• احفظ عملك بانتظام
• تأكد من إدخال جميع البيانات المطلوبة
• استخدم ميزة التحقق من البيانات قبل التصدير
• راجع الحسابات المالية بعناية
        """

        help_window = tk.Toplevel(self)
        help_window.title("دليل الاستخدام")
        help_window.geometry("600x500")
        help_window.configure(bg="#ffffff")

        tk.Label(help_window, text="دليل الاستخدام",
                font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        # إطار النص مع شريط التمرير
        text_frame = tk.Frame(help_window, bg="#ffffff")
        text_frame.pack(fill="both", expand=True, padx=20, pady=10)

        text_widget = tk.Text(text_frame, font=("Tajawal", 10), wrap="word",
                            relief="solid", bd=1)
        text_widget.pack(side="right", fill="both", expand=True)

        scrollbar = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")

        text_widget.insert("1.0", help_text.strip())
        text_widget.config(state="disabled")

        tk.Button(help_window, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=help_window.destroy, padx=20, pady=5).pack(pady=10)

    def on_closing(self):
        # حفظ تلقائي عند الإغلاق
        try:
            for frame in self.section_frames.values():
                frame.save_data()
            with open("last_project.json", 'w', encoding='utf-8') as f:
                json.dump(self.project_data.to_dict(), f, ensure_ascii=False, indent=2)
        except:
            pass
        self.destroy()

if __name__ == "__main__":
    app = MainApp()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()
