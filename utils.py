#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وظائف مساعدة لنظام دراسة الجدوى الشامل
"""

import os
import re
import json
import logging
import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional, Union

# إعداد نظام السجلات
def setup_logging(log_level: str = "INFO", log_file: Optional[str] = None):
    """إعداد نظام السجلات"""
    
    # تحديد مستوى السجل
    level_map = {
        "DEBUG": logging.DEBUG,
        "INFO": logging.INFO,
        "WARNING": logging.WARNING,
        "ERROR": logging.ERROR,
        "CRITICAL": logging.CRITICAL
    }
    level = level_map.get(log_level.upper(), logging.INFO)
    
    # تنسيق الرسائل
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # إعداد المعالج
    handlers = []
    
    # معالج وحدة التحكم
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)
    
    # معالج الملف إذا تم تحديده
    if log_file:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setFormatter(formatter)
        handlers.append(file_handler)
    
    # تكوين السجل الرئيسي
    logging.basicConfig(
        level=level,
        handlers=handlers,
        force=True
    )
    
    return logging.getLogger(__name__)

# وظائف التحقق من صحة البيانات
def validate_phone_number(phone: str) -> bool:
    """التحقق من صحة رقم الهاتف"""
    if not phone:
        return False
    
    # إزالة المسافات والرموز الإضافية
    clean_phone = re.sub(r'[\s\-\(\)]', '', phone)
    
    # التحقق من النمط
    pattern = r'^(\+966|966|0)?[5][0-9]{8}$'
    return bool(re.match(pattern, clean_phone))

def validate_email(email: str) -> bool:
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return True  # البريد الإلكتروني اختياري
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def validate_age(age: Union[str, int]) -> bool:
    """التحقق من صحة العمر"""
    try:
        age_int = int(age)
        return 18 <= age_int <= 100
    except (ValueError, TypeError):
        return False

def validate_amount(amount: Union[str, float]) -> bool:
    """التحقق من صحة المبلغ المالي"""
    try:
        if isinstance(amount, str):
            # إزالة الفواصل والرموز
            clean_amount = amount.replace(',', '').replace('ريال', '').strip()
            amount_float = float(clean_amount)
        else:
            amount_float = float(amount)
        
        return amount_float >= 0
    except (ValueError, TypeError):
        return False

# وظائف تنسيق البيانات
def format_currency(amount: Union[str, float], currency: str = "ريال") -> str:
    """تنسيق المبلغ المالي"""
    try:
        if isinstance(amount, str):
            amount = float(amount.replace(',', ''))
        
        formatted = f"{amount:,.2f}"
        return f"{formatted} {currency}"
    except (ValueError, TypeError):
        return "0.00 ريال"

def format_percentage(value: Union[str, float]) -> str:
    """تنسيق النسبة المئوية"""
    try:
        if isinstance(value, str):
            value = float(value)
        
        return f"{value:.1f}%"
    except (ValueError, TypeError):
        return "0.0%"

def format_date(date_obj: Optional[datetime.datetime] = None) -> str:
    """تنسيق التاريخ"""
    if date_obj is None:
        date_obj = datetime.datetime.now()
    
    return date_obj.strftime("%Y-%m-%d")

def format_datetime(datetime_obj: Optional[datetime.datetime] = None) -> str:
    """تنسيق التاريخ والوقت"""
    if datetime_obj is None:
        datetime_obj = datetime.datetime.now()
    
    return datetime_obj.strftime("%Y-%m-%d %H:%M:%S")

# وظائف معالجة النصوص
def clean_text(text: str) -> str:
    """تنظيف النص من الأحرف غير المرغوبة"""
    if not text:
        return ""
    
    # إزالة المسافات الزائدة
    text = re.sub(r'\s+', ' ', text.strip())
    
    # إزالة الأحرف الخاصة الضارة
    text = re.sub(r'[<>"\']', '', text)
    
    return text

def truncate_text(text: str, max_length: int = 100, suffix: str = "...") -> str:
    """اقتطاع النص إلى طول محدد"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def extract_numbers(text: str) -> List[float]:
    """استخراج الأرقام من النص"""
    pattern = r'-?\d+\.?\d*'
    matches = re.findall(pattern, text)
    return [float(match) for match in matches]

# وظائف معالجة الملفات
def safe_filename(filename: str) -> str:
    """إنشاء اسم ملف آمن"""
    # إزالة الأحرف غير المسموحة
    safe_chars = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # تحديد الطول
    if len(safe_chars) > 200:
        safe_chars = safe_chars[:200]
    
    return safe_chars.strip()

def get_file_size(filepath: Union[str, Path]) -> int:
    """الحصول على حجم الملف بالبايت"""
    try:
        return os.path.getsize(filepath)
    except OSError:
        return 0

def get_file_size_human(filepath: Union[str, Path]) -> str:
    """الحصول على حجم الملف بصيغة قابلة للقراءة"""
    size = get_file_size(filepath)
    
    for unit in ['B', 'KB', 'MB', 'GB']:
        if size < 1024.0:
            return f"{size:.1f} {unit}"
        size /= 1024.0
    
    return f"{size:.1f} TB"

def backup_file(filepath: Union[str, Path], backup_dir: Optional[Union[str, Path]] = None) -> Optional[str]:
    """إنشاء نسخة احتياطية من الملف"""
    try:
        filepath = Path(filepath)
        
        if not filepath.exists():
            return None
        
        if backup_dir is None:
            backup_dir = filepath.parent / "backups"
        else:
            backup_dir = Path(backup_dir)
        
        backup_dir.mkdir(exist_ok=True)
        
        # إنشاء اسم النسخة الاحتياطية
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"{filepath.stem}_backup_{timestamp}{filepath.suffix}"
        backup_path = backup_dir / backup_name
        
        # نسخ الملف
        import shutil
        shutil.copy2(filepath, backup_path)
        
        return str(backup_path)
    
    except Exception:
        return None

# وظائف الحسابات المالية
def calculate_roi(profit: float, investment: float) -> float:
    """حساب معدل العائد على الاستثمار"""
    if investment <= 0:
        return 0.0
    
    return (profit / investment) * 100

def calculate_payback_period(investment: float, annual_profit: float) -> float:
    """حساب فترة استرداد رأس المال بالسنوات"""
    if annual_profit <= 0:
        return float('inf')
    
    return investment / annual_profit

def calculate_profit_margin(profit: float, revenue: float) -> float:
    """حساب هامش الربح"""
    if revenue <= 0:
        return 0.0
    
    return (profit / revenue) * 100

def calculate_break_even(fixed_costs: float, variable_cost_per_unit: float, price_per_unit: float) -> float:
    """حساب نقطة التعادل"""
    if price_per_unit <= variable_cost_per_unit:
        return float('inf')
    
    return fixed_costs / (price_per_unit - variable_cost_per_unit)

# وظائف التحليل الإحصائي
def calculate_average(values: List[float]) -> float:
    """حساب المتوسط"""
    if not values:
        return 0.0
    
    return sum(values) / len(values)

def calculate_total(values: List[float]) -> float:
    """حساب المجموع"""
    return sum(values)

def calculate_growth_rate(old_value: float, new_value: float) -> float:
    """حساب معدل النمو"""
    if old_value <= 0:
        return 0.0
    
    return ((new_value - old_value) / old_value) * 100

# وظائف التصدير والاستيراد
def export_to_json(data: Dict[str, Any], filepath: Union[str, Path]) -> bool:
    """تصدير البيانات إلى JSON"""
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception:
        return False

def import_from_json(filepath: Union[str, Path]) -> Optional[Dict[str, Any]]:
    """استيراد البيانات من JSON"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception:
        return None

# وظائف التحقق من النظام
def check_disk_space(path: Union[str, Path], min_space_mb: int = 100) -> bool:
    """التحقق من وجود مساحة كافية على القرص"""
    try:
        import shutil
        free_space = shutil.disk_usage(path).free
        free_space_mb = free_space / (1024 * 1024)
        return free_space_mb >= min_space_mb
    except Exception:
        return True  # افتراض وجود مساحة كافية في حالة الخطأ

def check_write_permission(path: Union[str, Path]) -> bool:
    """التحقق من صلاحية الكتابة"""
    try:
        test_file = Path(path) / "test_write_permission.tmp"
        test_file.touch()
        test_file.unlink()
        return True
    except Exception:
        return False

# وظائف مساعدة للواجهة
def center_window(window, width: int, height: int):
    """توسيط النافذة على الشاشة"""
    screen_width = window.winfo_screenwidth()
    screen_height = window.winfo_screenheight()
    
    x = (screen_width - width) // 2
    y = (screen_height - height) // 2
    
    window.geometry(f"{width}x{height}+{x}+{y}")

def bind_mousewheel(widget, canvas):
    """ربط عجلة الماوس بالتمرير"""
    def on_mousewheel(event):
        canvas.yview_scroll(int(-1 * (event.delta / 120)), "units")
    
    widget.bind("<MouseWheel>", on_mousewheel)

# إعداد السجل الافتراضي
logger = setup_logging()
