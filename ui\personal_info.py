import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import modern_styles

class PersonalInfoFrame(tk.Frame):
    """واجهة المعلومات الشخصية لصاحب المشروع"""
    
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # تغيير لون الخلفية للتصميم الحديث
        self.configure(bg="#f8fafc")

        # إنشاء حاوية رئيسية مع تأثيرات حديثة
        container = tk.Frame(self, bg="#f8fafc")
        container.pack(fill="both", expand=True, padx=25, pady=20)

        # العنوان الرئيسي مع تصميم حديث
        header_card = tk.Frame(container, bg="#ffffff", relief="flat")
        header_card.pack(fill="x", pady=(0, 20))

        # إضافة تأثير الظل للعنوان
        self.add_shadow_effect(header_card)

        # محتوى العنوان
        header_content = tk.Frame(header_card, bg="#ffffff")
        header_content.pack(fill="x", padx=25, pady=20)

        # أيقونة ونص العنوان
        title_container = tk.Frame(header_content, bg="#ffffff")
        title_container.pack()

        # أيقونة حديثة
        icon_frame = tk.Frame(title_container, bg="#3b82f6", width=50, height=50)
        icon_frame.pack(side="right", padx=(0, 20))
        icon_frame.pack_propagate(False)

        icon_label = tk.Label(icon_frame, text="👤", font=("Arial", 20),
                             bg="#3b82f6", fg="white")
        icon_label.pack(expand=True)

        # العنوان مع تدرج
        title = tk.Label(title_container, text="المعلومات الشخصية لصاحب المشروع",
                        font=("Tajawal", 22, "bold"), bg="#ffffff", fg="#1e293b")
        title.pack(side="right")

        # خط فاصل مع تدرج
        separator = tk.Frame(header_content, bg="#e2e8f0", height=2)
        separator.pack(fill="x", pady=(15, 0))

        # إطار النموذج الرئيسي - مطابق لقسم بيانات المشروع
        main_form = tk.Frame(self, bg="#ffffff")
        main_form.pack(fill="both", expand=True, padx=40)

        # قسم المعلومات الأساسية - مطابق لقسم بيانات المشروع
        basic_section = tk.LabelFrame(main_form, text="المعلومات الأساسية",
                                    font=("Tajawal", 12, "bold"), bg="#ffffff",
                                    fg="#2c3e50", relief="solid", bd=1)
        basic_section.pack(fill="x", pady=10)

        # الحقول الأساسية
        basic_fields = [
            "اسم صاحب المشروع",
            "العمر",
            "الحالة الاجتماعية",
            "عدد أفراد الأسرة",
            "المؤهل العلمي",
            "رقم الهاتف",
            "رقم هاتف شخص معرف",
            "مكان السكن"
        ]
        
        # إنشاء الحقول - مطابق لقسم بيانات المشروع
        for i, field in enumerate(basic_fields):
            field_frame = tk.Frame(basic_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)

            # التسمية - مطابقة لقسم بيانات المشروع
            tk.Label(field_frame, text=field, font=("Tajawal", 12, "bold"),
                    bg="#ffffff", fg="#2c3e50", width=25, anchor="e").pack(side="right", padx=(0, 10))
            
            # حقل الإدخال - مطابق لقسم بيانات المشروع
            if field == "الحالة الاجتماعية":
                entry = modern_styles.create_rtl_combobox(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    values=["أعزب", "متزوج", "مطلق", "أرمل"]
                )
            elif field == "المؤهل العلمي":
                entry = modern_styles.create_rtl_combobox(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    values=["ابتدائي", "متوسط", "ثانوي", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه"]
                )
            else:
                entry = modern_styles.create_rtl_entry(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    bg=modern_styles.get_color("background"),
                    fg=modern_styles.get_color("text_primary")
                )

            entry.pack(side="right", fill="x", expand=True)
            self.entries[field] = entry

        # أزرار العمليات - مطابقة لقسم بيانات المشروع
        buttons_frame = tk.Frame(main_form, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)

        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat",
                    "padx": 20, "pady": 8, "cursor": "hand2"}

        tk.Button(buttons_frame, text="💾 حفظ البيانات", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
        
        # قسم معلومات إضافية
        additional_frame = tk.LabelFrame(main_form, text="معلومات إضافية", 
                                       font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                       fg="#2c3e50", relief="solid", bd=1)
        additional_frame.pack(fill="x", pady=20)
        
        # الخبرة السابقة
        exp_frame = tk.Frame(additional_frame, bg="#ffffff")
        exp_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(exp_frame, text="الخبرة السابقة:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        exp_text = tk.Text(exp_frame, font=("Tajawal", 11), height=3, width=60,
                          relief="solid", bd=1, wrap="word")
        exp_text.pack(fill="x", pady=5)
        self.entries["الخبرة السابقة"] = exp_text
        
        # الهدف من المشروع
        goal_frame = tk.Frame(additional_frame, bg="#ffffff")
        goal_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(goal_frame, text="الهدف من المشروع:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        goal_text = tk.Text(goal_frame, font=("Tajawal", 11), height=3, width=60,
                           relief="solid", bd=1, wrap="word")
        goal_text.pack(fill="x", pady=5)
        self.entries["الهدف من المشروع"] = goal_text
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)
        
        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat", 
                    "padx": 20, "pady": 8, "cursor": "hand2"}
        
        tk.Button(buttons_frame, text="💾 حفظ البيانات", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
    
    def save_data(self):
        """حفظ البيانات في نموذج البيانات والانتقال للقسم التالي"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = entry.get("1.0", tk.END).strip()
            else:
                value = entry.get().strip()
            self.project_data.personal_info[field] = value

        # الانتقال إلى القسم التالي
        if hasattr(self.master, 'go_to_next_section'):
            self.master.go_to_next_section()
        else:
            # البحث عن النافذة الرئيسية
            parent = self.master
            while parent and not hasattr(parent, 'go_to_next_section'):
                parent = parent.master
            if parent and hasattr(parent, 'go_to_next_section'):
                parent.go_to_next_section()
    
    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        for field, entry in self.entries.items():
            value = self.project_data.personal_info.get(field, "")
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
                entry.insert("1.0", value)
            else:
                entry.delete(0, tk.END)
                entry.insert(0, value)

    def clear_data(self):
        """مسح جميع البيانات"""
        for entry in self.entries.values():
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
            else:
                entry.delete(0, tk.END)
    
    def clear_data(self):
        """مسح جميع البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
            else:
                entry.delete(0, tk.END)
        
        # مسح البيانات من النموذج
        self.project_data.personal_info.clear()
    
    def validate_data(self):
        """التحقق من صحة البيانات"""
        required_fields = ["اسم صاحب المشروع", "العمر", "رقم الهاتف"]
        missing_fields = []
        
        for field in required_fields:
            if field in self.entries:
                value = self.entries[field].get().strip()
                if not value:
                    missing_fields.append(field)
        
        return missing_fields

    def add_shadow_effect(self, widget):
        """إضافة تأثير الظل للعنصر"""
        # إنشاء ظل بسيط باستخدام إطار خلفي
        shadow = tk.Frame(widget.master, bg="#e2e8f0", height=2)
        shadow.place(in_=widget, x=3, y=3, relwidth=1, relheight=1)
        shadow.lower()

    def create_info_card(self, parent, title, color):
        """إنشاء بطاقة معلومات حديثة"""
        # إطار البطاقة الرئيسي
        card = tk.Frame(parent, bg="#ffffff", relief="flat")
        card.pack(fill="x", pady=15)

        # إضافة تأثير الظل
        self.add_shadow_effect(card)

        # عنوان البطاقة
        header = tk.Frame(card, bg=color)
        header.pack(fill="x")

        title_label = tk.Label(header, text=title,
                              font=("Tajawal", 14, "bold"),
                              bg=color, fg="white", pady=12)
        title_label.pack()

        # محتوى البطاقة
        content = tk.Frame(card, bg="#ffffff")
        content.pack(fill="both", expand=True, padx=20, pady=20)

        return content

    def create_modern_field(self, parent, field_name):
        """إنشاء حقل إدخال حديث مع دعم RTL"""
        # إطار الحقل
        field_frame = tk.Frame(parent, bg="#ffffff")
        field_frame.pack(fill="x", pady=12)

        # التسمية مع دعم RTL
        label = modern_styles.create_rtl_label(
            field_frame,
            text=field_name,
            font=modern_styles.get_safe_font("body_bold"),
            bg="#ffffff",
            fg=modern_styles.get_color("text_primary")
        )
        label.pack(anchor="e", pady=(0, 8))

        # حقل الإدخال مع تصميم حديث ودعم RTL
        if field_name == "الحالة الاجتماعية":
            entry = modern_styles.create_rtl_combobox(
                field_frame,
                font=modern_styles.get_safe_font("body"),
                values=["أعزب", "متزوج", "مطلق", "أرمل"]
            )
        elif field_name == "المؤهل العلمي":
            entry = modern_styles.create_rtl_combobox(
                field_frame,
                font=modern_styles.get_safe_font("body"),
                values=["ابتدائي", "متوسط", "ثانوي", "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه"]
            )
        else:
            entry = modern_styles.create_rtl_entry(
                field_frame,
                font=modern_styles.get_safe_font("body"),
                bg=modern_styles.get_color("background"),
                fg=modern_styles.get_color("text_primary"),
                highlightcolor=modern_styles.get_color("primary"),
                highlightbackground=modern_styles.get_color("border"),
                insertbackground=modern_styles.get_color("text_primary")
            )

        entry.pack(fill="x", ipady=12)
        self.entries[field_name] = entry
