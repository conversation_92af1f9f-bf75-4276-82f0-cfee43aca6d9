#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف التكوين لنظام دراسة الجدوى الشامل
يحتوي على الإعدادات والثوابت المستخدمة في النظام
"""

import os
from pathlib import Path

# معلومات التطبيق
APP_NAME = "نظام دراسة الجدوى الشامل"
APP_VERSION = "1.0.0"
APP_AUTHOR = "نظام دراسة الجدوى"
APP_DESCRIPTION = "برنامج شامل لإعداد دراسات الجدوى الاقتصادية"

# إعدادات الواجهة
UI_CONFIG = {
    "window_title": APP_NAME,
    "window_size": "1400x900",
    "window_min_size": (1000, 700),
    "background_color": "#f2f4f8",
    "primary_color": "#4a90e2",
    "secondary_color": "#1e3a8a",
    "success_color": "#27ae60",
    "warning_color": "#f39c12",
    "error_color": "#e74c3c",
    "text_color": "#2c3e50",
    "sidebar_color": "#e7eefa",
    "card_color": "#ffffff"
}

# إعدادات الخطوط
FONTS = {
    "title": ("Tajawal", 18, "bold"),
    "heading": ("Tajawal", 14, "bold"),
    "subheading": ("Tajawal", 12, "bold"),
    "normal": ("Tajawal", 11),
    "small": ("Tajawal", 10),
    "button": ("Tajawal", 11, "bold")
}

# مسارات الملفات والمجلدات
BASE_DIR = Path(__file__).parent
DATA_DIR = BASE_DIR / "data"
UI_DIR = BASE_DIR / "ui"
ASSETS_DIR = BASE_DIR / "assets"
EXPORTS_DIR = BASE_DIR / "exports"
PROJECTS_DIR = DATA_DIR / "projects"
BACKUPS_DIR = DATA_DIR / "backups"
TEMPLATES_DIR = DATA_DIR / "templates"

# إنشاء المجلدات إذا لم تكن موجودة
for directory in [DATA_DIR, EXPORTS_DIR, PROJECTS_DIR, BACKUPS_DIR, TEMPLATES_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# إعدادات الحفظ التلقائي
AUTO_SAVE_CONFIG = {
    "enabled": True,
    "interval_minutes": 5,
    "max_backups": 10,
    "backup_on_save": True
}

# إعدادات التصدير
EXPORT_CONFIG = {
    "pdf": {
        "page_size": "A4",
        "margins": {
            "top": 72,
            "bottom": 18,
            "left": 72,
            "right": 72
        },
        "font_size": {
            "title": 18,
            "heading": 14,
            "normal": 10
        }
    },
    "word": {
        "page_size": "A4",
        "margins": 1.0,  # بوصة
        "font_name": "Arial",
        "font_size": 11
    },
    "excel": {
        "sheet_names": {
            "startup_costs": "تكاليف التأسيس",
            "fixed_capital": "رأس المال الثابت",
            "working_capital": "رأس المال العامل",
            "monthly_profits": "الأرباح الشهرية",
            "yearly_revenues": "الإيرادات السنوية",
            "equipment": "المعدات",
            "raw_materials": "المواد الخام"
        }
    }
}

# إعدادات التحقق من البيانات
VALIDATION_CONFIG = {
    "required_personal_fields": [
        "اسم صاحب المشروع",
        "العمر", 
        "رقم الهاتف"
    ],
    "required_project_fields": [
        "اسم المشروع",
        "موقع المشروع"
    ],
    "min_age": 18,
    "max_age": 100,
    "phone_pattern": r"^[0-9+\-\s()]+$"
}

# رسائل النظام
MESSAGES = {
    "success": {
        "save": "تم حفظ البيانات بنجاح",
        "load": "تم تحميل البيانات بنجاح",
        "export": "تم تصدير التقرير بنجاح",
        "validation": "جميع البيانات صحيحة!",
        "auto_save": "تم الحفظ التلقائي"
    },
    "error": {
        "save": "فشل في حفظ البيانات",
        "load": "فشل في تحميل البيانات",
        "export": "فشل في تصدير التقرير",
        "validation": "توجد أخطاء في البيانات",
        "missing_data": "بيانات مطلوبة مفقودة"
    },
    "warning": {
        "unsaved_changes": "توجد تغييرات غير محفوظة",
        "incomplete_data": "البيانات غير مكتملة",
        "overwrite": "سيتم استبدال البيانات الموجودة"
    },
    "info": {
        "auto_save_disabled": "الحفظ التلقائي معطل",
        "backup_created": "تم إنشاء نسخة احتياطية",
        "export_location": "تم حفظ الملف في: {path}"
    }
}

# قوائم البيانات الثابتة
STATIC_DATA = {
    "marital_status": ["أعزب", "متزوج", "مطلق", "أرمل"],
    "education_levels": [
        "ابتدائي", "متوسط", "ثانوي", 
        "دبلوم", "بكالوريوس", "ماجستير", "دكتوراه"
    ],
    "months": [
        "يناير", "فبراير", "مارس", "أبريل", 
        "مايو", "يونيو", "يوليو", "أغسطس",
        "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
    ],
    "cost_types": ["ثابت", "متغير"],
    "currencies": ["دينار عراقي", "دولار أمريكي"],
    "currency_symbols": {"دينار عراقي": "د.ع", "دولار أمريكي": "$"},
    "default_currency": "دينار عراقي",
    "exchange_rate": 1500,  # سعر صرف الدولار مقابل الدينار العراقي
    "show_dual_currency": True  # عرض العملتين معاً
}

# إعدادات الحسابات المالية
FINANCIAL_CONFIG = {
    "depreciation_rate": 0.10,  # 10% سنوياً
    "roi_thresholds": {
        "excellent": 25,
        "very_good": 20,
        "good": 15,
        "acceptable": 10,
        "poor": 5
    },
    "profit_margin_thresholds": {
        "excellent": 20,
        "very_good": 15,
        "good": 10,
        "acceptable": 5,
        "poor": 0
    }
}

# إعدادات الأيقونات والرموز
ICONS = {
    "save": "💾",
    "load": "📁",
    "new": "📄",
    "export": "📊",
    "pdf": "📄",
    "word": "📝",
    "excel": "📊",
    "validate": "✅",
    "error": "❌",
    "warning": "⚠️",
    "info": "ℹ️",
    "success": "✅",
    "calculate": "🧮",
    "analysis": "📈",
    "report": "📋",
    "statistics": "📊",
    "help": "❓",
    "about": "ℹ️"
}

# إعدادات قاعدة البيانات (للاستخدام المستقبلي)
DATABASE_CONFIG = {
    "type": "sqlite",
    "name": "feasibility_study.db",
    "path": DATA_DIR / "feasibility_study.db",
    "backup_interval": 24,  # ساعة
    "max_connections": 10
}

# إعدادات الأمان
SECURITY_CONFIG = {
    "encrypt_data": False,  # للاستخدام المستقبلي
    "backup_encryption": False,
    "max_file_size": 50 * 1024 * 1024,  # 50 MB
    "allowed_extensions": [".json", ".txt", ".pdf", ".docx", ".xlsx"]
}

# إعدادات الأداء
PERFORMANCE_CONFIG = {
    "max_undo_levels": 10,
    "cache_size": 100,  # عدد العمليات المحفوظة في الذاكرة
    "lazy_loading": True,
    "compress_backups": False
}

# إعدادات التطوير والتصحيح
DEBUG_CONFIG = {
    "enabled": False,
    "log_level": "INFO",
    "log_file": DATA_DIR / "debug.log",
    "show_performance_metrics": False,
    "enable_profiling": False
}

def get_config(section, key=None, default=None):
    """الحصول على قيمة من التكوين"""
    config_map = {
        "ui": UI_CONFIG,
        "fonts": FONTS,
        "auto_save": AUTO_SAVE_CONFIG,
        "export": EXPORT_CONFIG,
        "validation": VALIDATION_CONFIG,
        "messages": MESSAGES,
        "static_data": STATIC_DATA,
        "financial": FINANCIAL_CONFIG,
        "icons": ICONS,
        "database": DATABASE_CONFIG,
        "security": SECURITY_CONFIG,
        "performance": PERFORMANCE_CONFIG,
        "debug": DEBUG_CONFIG
    }
    
    if section not in config_map:
        return default
    
    if key is None:
        return config_map[section]
    
    return config_map[section].get(key, default)

def update_config(section, key, value):
    """تحديث قيمة في التكوين"""
    config_map = {
        "ui": UI_CONFIG,
        "fonts": FONTS,
        "auto_save": AUTO_SAVE_CONFIG,
        "export": EXPORT_CONFIG,
        "validation": VALIDATION_CONFIG,
        "financial": FINANCIAL_CONFIG,
        "debug": DEBUG_CONFIG
    }
    
    if section in config_map:
        config_map[section][key] = value
        return True
    return False

# تحميل إعدادات مخصصة إذا وجدت
def load_custom_config():
    """تحميل إعدادات مخصصة من ملف"""
    config_file = DATA_DIR / "custom_config.json"
    if config_file.exists():
        try:
            import json
            with open(config_file, 'r', encoding='utf-8') as f:
                custom_config = json.load(f)
            
            # تطبيق الإعدادات المخصصة
            for section, settings in custom_config.items():
                if isinstance(settings, dict):
                    for key, value in settings.items():
                        update_config(section, key, value)
            
            return True
        except Exception:
            return False
    return False

# تحميل الإعدادات المخصصة عند استيراد الوحدة
load_custom_config()
