"""
مدير التصدير لنظام دراسة الجدوى
يوفر وظائف التصدير إلى PDF وWord مع دعم اللغة العربية
"""

import os
import datetime
from pathlib import Path
from .models import ProjectData

class ExportManager:
    """مدير التصدير الرئيسي"""
    
    def __init__(self):
        self.export_folder = Path("exports")
        self.export_folder.mkdir(exist_ok=True)
    
    def export_to_pdf(self, project_data: ProjectData, filename: str = None):
        """تصدير إلى PDF"""
        try:
            # محاولة استيراد المكتبات المطلوبة
            try:
                from reportlab.lib.pagesizes import A4
                from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
                from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
                from reportlab.lib.units import inch
                from reportlab.lib import colors
                from reportlab.pdfbase import pdfmetrics
                from reportlab.pdfbase.ttfonts import TTFont
            except ImportError:
                raise Exception("مكتبة reportlab غير مثبتة. يرجى تثبيتها باستخدام: pip install reportlab")
            
            if not filename:
                project_name = project_data.project_info.get("اسم المشروع", "تقرير_دراسة_جدوى")
                safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{safe_name}_{timestamp}.pdf"
            
            filepath = self.export_folder / filename
            
            # إنشاء المستند
            doc = SimpleDocTemplate(str(filepath), pagesize=A4, rightMargin=72, leftMargin=72,
                                  topMargin=72, bottomMargin=18)
            
            # إعداد الأنماط
            styles = getSampleStyleSheet()
            
            # محاولة تحميل خط عربي (إذا كان متوفراً)
            try:
                # يمكن إضافة خط عربي هنا إذا كان متوفراً
                arabic_font_path = "assets/fonts/NotoSansArabic-Regular.ttf"
                if os.path.exists(arabic_font_path):
                    pdfmetrics.registerFont(TTFont('Arabic', arabic_font_path))
                    font_name = 'Arabic'
                else:
                    font_name = 'Helvetica'
            except:
                font_name = 'Helvetica'
            
            # أنماط مخصصة
            title_style = ParagraphStyle(
                'CustomTitle',
                parent=styles['Heading1'],
                fontSize=18,
                spaceAfter=30,
                alignment=1,  # توسيط
                fontName=font_name
            )
            
            heading_style = ParagraphStyle(
                'CustomHeading',
                parent=styles['Heading2'],
                fontSize=14,
                spaceAfter=12,
                fontName=font_name
            )
            
            normal_style = ParagraphStyle(
                'CustomNormal',
                parent=styles['Normal'],
                fontSize=10,
                fontName=font_name
            )
            
            # محتوى المستند
            story = []
            
            # العنوان الرئيسي
            story.append(Paragraph("تقرير دراسة الجدوى الشامل", title_style))
            story.append(Spacer(1, 12))
            
            # معلومات المشروع
            project_name = project_data.project_info.get("اسم المشروع", "غير محدد")
            story.append(Paragraph(f"اسم المشروع: {project_name}", heading_style))
            story.append(Spacer(1, 12))
            
            # تاريخ التقرير
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            story.append(Paragraph(f"تاريخ التقرير: {current_date}", normal_style))
            story.append(Spacer(1, 20))
            
            # المعلومات الشخصية
            if project_data.personal_info:
                story.append(Paragraph("المعلومات الشخصية", heading_style))
                personal_data = []
                for key, value in project_data.personal_info.items():
                    if value:
                        personal_data.append([key, str(value)])
                
                if personal_data:
                    table = Table(personal_data, colWidths=[2*inch, 3*inch])
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                        ('FONTNAME', (0, 0), (-1, 0), font_name),
                        ('FONTSIZE', (0, 0), (-1, 0), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(table)
                    story.append(Spacer(1, 20))
            
            # بيانات المشروع
            if project_data.project_info:
                story.append(Paragraph("بيانات المشروع", heading_style))
                project_info_data = []
                for key, value in project_data.project_info.items():
                    if value and key != "اسم المشروع":  # تجنب التكرار
                        project_info_data.append([key, str(value)])
                
                if project_info_data:
                    table = Table(project_info_data, colWidths=[2*inch, 3*inch])
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                        ('FONTNAME', (0, 0), (-1, 0), font_name),
                        ('FONTSIZE', (0, 0), (-1, 0), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(table)
                    story.append(Spacer(1, 20))
            
            # تحليل SWOT
            if any(project_data.swot.values()):
                story.append(Paragraph("تحليل SWOT", heading_style))
                for key, value in project_data.swot.items():
                    if value:
                        story.append(Paragraph(f"<b>{key}:</b>", normal_style))
                        story.append(Paragraph(str(value), normal_style))
                        story.append(Spacer(1, 10))
                story.append(Spacer(1, 10))
            
            # الملخص المالي
            if project_data.summary:
                story.append(Paragraph("الملخص المالي", heading_style))
                financial_data = []
                for key, value in project_data.summary.items():
                    if value and key != "ملاحظات إضافية":
                        financial_data.append([key, str(value)])
                
                if financial_data:
                    table = Table(financial_data, colWidths=[2.5*inch, 2.5*inch])
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                        ('FONTNAME', (0, 0), (-1, 0), font_name),
                        ('FONTSIZE', (0, 0), (-1, 0), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.lightblue),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(table)
                    story.append(Spacer(1, 20))
            
            # إحصائيات المشروع
            stats = project_data.get_summary_statistics()
            if stats:
                story.append(Paragraph("إحصائيات المشروع", heading_style))
                stats_data = [
                    ["نسبة اكتمال المشروع", f"{stats.get('completion_rate', 0):.1f}%"],
                    ["الأقسام المكتملة", f"{stats.get('completed_sections', 0)} من {stats.get('total_sections', 0)}"]
                ]
                
                if 'total_investment' in stats:
                    stats_data.append(["إجمالي الاستثمار المطلوب", f"{stats['total_investment']:,.2f} ريال"])
                
                table = Table(stats_data, colWidths=[2.5*inch, 2.5*inch])
                table.setStyle(TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                    ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
                    ('FONTNAME', (0, 0), (-1, 0), font_name),
                    ('FONTSIZE', (0, 0), (-1, 0), 12),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                    ('GRID', (0, 0), (-1, -1), 1, colors.black)
                ]))
                story.append(table)
            
            # بناء المستند
            doc.build(story)
            
            return str(filepath)
            
        except Exception as e:
            raise Exception(f"فشل في تصدير PDF: {str(e)}")
    
    def export_to_word(self, project_data: ProjectData, filename: str = None):
        """تصدير إلى Word"""
        try:
            # محاولة استيراد المكتبة المطلوبة
            try:
                from docx import Document
                from docx.shared import Inches
                from docx.enum.text import WD_ALIGN_PARAGRAPH
            except ImportError:
                raise Exception("مكتبة python-docx غير مثبتة. يرجى تثبيتها باستخدام: pip install python-docx")
            
            if not filename:
                project_name = project_data.project_info.get("اسم المشروع", "تقرير_دراسة_جدوى")
                safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{safe_name}_{timestamp}.docx"
            
            filepath = self.export_folder / filename
            
            # إنشاء المستند
            doc = Document()
            
            # العنوان الرئيسي
            title = doc.add_heading('تقرير دراسة الجدوى الشامل', 0)
            title.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # معلومات المشروع
            project_name = project_data.project_info.get("اسم المشروع", "غير محدد")
            doc.add_heading(f'اسم المشروع: {project_name}', level=1)
            
            # تاريخ التقرير
            current_date = datetime.datetime.now().strftime("%Y-%m-%d")
            p = doc.add_paragraph(f'تاريخ التقرير: {current_date}')
            p.alignment = WD_ALIGN_PARAGRAPH.RIGHT
            
            # المعلومات الشخصية
            if project_data.personal_info:
                doc.add_heading('المعلومات الشخصية', level=1)
                table = doc.add_table(rows=1, cols=2)
                table.style = 'Table Grid'
                
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'البيان'
                hdr_cells[1].text = 'القيمة'
                
                for key, value in project_data.personal_info.items():
                    if value:
                        row_cells = table.add_row().cells
                        row_cells[0].text = key
                        row_cells[1].text = str(value)
            
            # بيانات المشروع
            if project_data.project_info:
                doc.add_heading('بيانات المشروع', level=1)
                table = doc.add_table(rows=1, cols=2)
                table.style = 'Table Grid'
                
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'البيان'
                hdr_cells[1].text = 'القيمة'
                
                for key, value in project_data.project_info.items():
                    if value and key != "اسم المشروع":
                        row_cells = table.add_row().cells
                        row_cells[0].text = key
                        row_cells[1].text = str(value)
            
            # تحليل SWOT
            if any(project_data.swot.values()):
                doc.add_heading('تحليل SWOT', level=1)
                for key, value in project_data.swot.items():
                    if value:
                        doc.add_heading(key, level=2)
                        doc.add_paragraph(str(value))
            
            # المزيج التسويقي
            if any(project_data.marketing_mix.values()):
                doc.add_heading('المزيج التسويقي', level=1)
                for key, value in project_data.marketing_mix.items():
                    if value:
                        doc.add_heading(key, level=2)
                        doc.add_paragraph(str(value))
            
            # الملخص المالي
            if project_data.summary:
                doc.add_heading('الملخص المالي', level=1)
                table = doc.add_table(rows=1, cols=2)
                table.style = 'Table Grid'
                
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'البيان'
                hdr_cells[1].text = 'القيمة'
                
                for key, value in project_data.summary.items():
                    if value and key != "ملاحظات إضافية":
                        row_cells = table.add_row().cells
                        row_cells[0].text = key
                        row_cells[1].text = str(value)
                
                # إضافة الملاحظات إذا وجدت
                notes = project_data.summary.get("ملاحظات إضافية", "")
                if notes:
                    doc.add_heading('ملاحظات إضافية', level=2)
                    doc.add_paragraph(notes)
            
            # إحصائيات المشروع
            stats = project_data.get_summary_statistics()
            if stats:
                doc.add_heading('إحصائيات المشروع', level=1)
                table = doc.add_table(rows=1, cols=2)
                table.style = 'Table Grid'
                
                hdr_cells = table.rows[0].cells
                hdr_cells[0].text = 'المؤشر'
                hdr_cells[1].text = 'القيمة'
                
                stats_data = [
                    ("نسبة اكتمال المشروع", f"{stats.get('completion_rate', 0):.1f}%"),
                    ("الأقسام المكتملة", f"{stats.get('completed_sections', 0)} من {stats.get('total_sections', 0)}")
                ]
                
                if 'total_investment' in stats:
                    stats_data.append(("إجمالي الاستثمار المطلوب", f"{stats['total_investment']:,.2f} ريال"))
                
                for key, value in stats_data:
                    row_cells = table.add_row().cells
                    row_cells[0].text = key
                    row_cells[1].text = value
            
            # حفظ المستند
            doc.save(str(filepath))
            
            return str(filepath)
            
        except Exception as e:
            raise Exception(f"فشل في تصدير Word: {str(e)}")
    
    def export_to_excel(self, project_data: ProjectData, filename: str = None):
        """تصدير البيانات المالية إلى Excel"""
        try:
            # محاولة استيراد المكتبة المطلوبة
            try:
                import pandas as pd
            except ImportError:
                raise Exception("مكتبة pandas غير مثبتة. يرجى تثبيتها باستخدام: pip install pandas openpyxl")
            
            if not filename:
                project_name = project_data.project_info.get("اسم المشروع", "بيانات_مالية")
                safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{safe_name}_{timestamp}.xlsx"
            
            filepath = self.export_folder / filename
            
            # إنشاء كاتب Excel
            with pd.ExcelWriter(str(filepath), engine='openpyxl') as writer:
                
                # تصدير تكاليف التأسيس
                if project_data.financials.get('startup_costs'):
                    startup_df = pd.DataFrame(project_data.financials['startup_costs'])
                    startup_df.to_excel(writer, sheet_name='تكاليف التأسيس', index=False)
                
                # تصدير رأس المال الثابت
                if project_data.financials.get('fixed_capital'):
                    fixed_df = pd.DataFrame(project_data.financials['fixed_capital'])
                    fixed_df.to_excel(writer, sheet_name='رأس المال الثابت', index=False)
                
                # تصدير رأس المال العامل
                if project_data.financials.get('working_capital'):
                    working_df = pd.DataFrame(project_data.financials['working_capital'])
                    working_df.to_excel(writer, sheet_name='رأس المال العامل', index=False)
                
                # تصدير الأرباح الشهرية
                if project_data.financials.get('monthly_profits'):
                    profits_df = pd.DataFrame(project_data.financials['monthly_profits'])
                    profits_df.to_excel(writer, sheet_name='الأرباح الشهرية', index=False)
                
                # تصدير الإيرادات السنوية
                if project_data.financials.get('yearly_revenues'):
                    yearly_df = pd.DataFrame(project_data.financials['yearly_revenues'])
                    yearly_df.to_excel(writer, sheet_name='الإيرادات السنوية', index=False)
                
                # تصدير المعدات
                if project_data.production.get('equipment'):
                    equipment_df = pd.DataFrame(project_data.production['equipment'])
                    equipment_df.to_excel(writer, sheet_name='المعدات', index=False)
                
                # تصدير المواد الخام
                if project_data.production.get('raw_materials'):
                    materials_df = pd.DataFrame(project_data.production['raw_materials'])
                    materials_df.to_excel(writer, sheet_name='المواد الخام', index=False)
            
            return str(filepath)
            
        except Exception as e:
            raise Exception(f"فشل في تصدير Excel: {str(e)}")
