#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار دعم RTL للغة العربية
"""

import tkinter as tk
from tkinter import ttk
import modern_styles

def test_rtl_components():
    """اختبار مكونات RTL"""
    
    # إنشاء النافذة الرئيسية
    root = tk.Tk()
    root.title("اختبار دعم RTL للغة العربية")
    root.geometry("800x600")
    root.configure(bg=modern_styles.get_color("background"))
    
    # إطار رئيسي
    main_frame = tk.Frame(root, bg=modern_styles.get_color("background"))
    main_frame.pack(fill="both", expand=True, padx=20, pady=20)
    
    # العنوان
    title = modern_styles.create_rtl_label(
        main_frame,
        text="🧪 اختبار دعم اللغة العربية RTL",
        font=modern_styles.get_safe_font("title"),
        bg=modern_styles.get_color("background"),
        fg=modern_styles.get_color("primary")
    )
    title.pack(pady=(0, 20))
    
    # بطاقة اختبار حقول الإدخال
    input_card = tk.Frame(main_frame, bg=modern_styles.get_color("surface"), relief="flat", bd=1)
    input_card.pack(fill="x", pady=10)
    
    # عنوان البطاقة
    card_header = tk.Frame(input_card, bg=modern_styles.get_color("primary"))
    card_header.pack(fill="x")
    
    header_label = modern_styles.create_rtl_label(
        card_header,
        text="📝 حقول الإدخال مع دعم RTL",
        font=modern_styles.get_safe_font("heading"),
        bg=modern_styles.get_color("primary"),
        fg=modern_styles.get_color("text_white")
    )
    header_label.pack(pady=10)
    
    # محتوى البطاقة
    card_content = tk.Frame(input_card, bg=modern_styles.get_color("surface"))
    card_content.pack(fill="both", expand=True, padx=20, pady=20)
    
    # اختبار حقل إدخال نص
    text_label = modern_styles.create_rtl_label(
        card_content,
        text="اسم المستخدم:",
        font=modern_styles.get_safe_font("body_bold"),
        bg=modern_styles.get_color("surface"),
        fg=modern_styles.get_color("text_primary")
    )
    text_label.pack(anchor="e", pady=(0, 5))
    
    text_entry = modern_styles.create_rtl_entry(
        card_content,
        font=modern_styles.get_safe_font("body")
    )
    text_entry.pack(fill="x", pady=(0, 15), ipady=8)
    text_entry.insert(0, "أدخل اسمك هنا")
    
    # اختبار قائمة منسدلة
    combo_label = modern_styles.create_rtl_label(
        card_content,
        text="اختر المدينة:",
        font=modern_styles.get_safe_font("body_bold"),
        bg=modern_styles.get_color("surface"),
        fg=modern_styles.get_color("text_primary")
    )
    combo_label.pack(anchor="e", pady=(0, 5))
    
    combo_box = modern_styles.create_rtl_combobox(
        card_content,
        values=["الرياض", "جدة", "الدمام", "مكة المكرمة", "المدينة المنورة"],
        font=modern_styles.get_safe_font("body")
    )
    combo_box.pack(fill="x", pady=(0, 15), ipady=8)
    combo_box.set("اختر المدينة")
    
    # اختبار مربع نص
    textarea_label = modern_styles.create_rtl_label(
        card_content,
        text="وصف المشروع:",
        font=modern_styles.get_safe_font("body_bold"),
        bg=modern_styles.get_color("surface"),
        fg=modern_styles.get_color("text_primary")
    )
    textarea_label.pack(anchor="e", pady=(0, 5))
    
    textarea = modern_styles.create_rtl_text(
        card_content,
        height=5,
        font=modern_styles.get_safe_font("body")
    )
    textarea.pack(fill="x", pady=(0, 15))
    textarea.insert("1.0", "اكتب وصف مشروعك هنا...\nيمكنك الكتابة بالعربية من اليمين إلى اليسار")
    
    # بطاقة اختبار الخطوط
    font_card = tk.Frame(main_frame, bg=modern_styles.get_color("surface"), relief="flat", bd=1)
    font_card.pack(fill="x", pady=10)
    
    # عنوان بطاقة الخطوط
    font_header = tk.Frame(font_card, bg=modern_styles.get_color("secondary"))
    font_header.pack(fill="x")
    
    font_header_label = modern_styles.create_rtl_label(
        font_header,
        text="🔤 اختبار الخطوط العربية",
        font=modern_styles.get_safe_font("heading"),
        bg=modern_styles.get_color("secondary"),
        fg=modern_styles.get_color("text_white")
    )
    font_header_label.pack(pady=10)
    
    # محتوى بطاقة الخطوط
    font_content = tk.Frame(font_card, bg=modern_styles.get_color("surface"))
    font_content.pack(fill="both", expand=True, padx=20, pady=20)
    
    # اختبار خطوط مختلفة
    fonts_to_test = [
        ("title", "عنوان رئيسي - خط كبير"),
        ("heading", "عنوان فرعي - خط متوسط"),
        ("body", "نص عادي - خط أساسي"),
        ("small", "نص صغير - للتفاصيل")
    ]
    
    for font_name, sample_text in fonts_to_test:
        font_sample = modern_styles.create_rtl_label(
            font_content,
            text=f"{sample_text} - {font_name}",
            font=modern_styles.get_safe_font(font_name),
            bg=modern_styles.get_color("surface"),
            fg=modern_styles.get_color("text_primary")
        )
        font_sample.pack(anchor="e", pady=5)
    
    # بطاقة معلومات
    info_card = tk.Frame(main_frame, bg=modern_styles.get_color("surface"), relief="flat", bd=1)
    info_card.pack(fill="x", pady=10)
    
    # عنوان بطاقة المعلومات
    info_header = tk.Frame(info_card, bg=modern_styles.get_color("accent"))
    info_header.pack(fill="x")
    
    info_header_label = modern_styles.create_rtl_label(
        info_header,
        text="ℹ️ معلومات دعم RTL",
        font=modern_styles.get_safe_font("heading"),
        bg=modern_styles.get_color("accent"),
        fg=modern_styles.get_color("text_white")
    )
    info_header_label.pack(pady=10)
    
    # محتوى بطاقة المعلومات
    info_content = tk.Frame(info_card, bg=modern_styles.get_color("surface"))
    info_content.pack(fill="both", expand=True, padx=20, pady=20)
    
    info_items = [
        "✅ تطبيق اتجاه RTL على مكون Input الأساسي",
        "✅ تطبيق اتجاه RTL على مكون Textarea الأساسي", 
        "✅ إضافة خط Noto Sans Arabic لدعم اللغة العربية بشكل أفضل",
        "✅ محاذاة النصوص من اليمين إلى اليسار",
        "✅ دعم الكتابة العربية في جميع الحقول",
        "✅ خطوط احتياطية في حالة عدم توفر الخط الأساسي"
    ]
    
    for item in info_items:
        info_label = modern_styles.create_rtl_label(
            info_content,
            text=item,
            font=modern_styles.get_safe_font("body"),
            bg=modern_styles.get_color("surface"),
            fg=modern_styles.get_color("text_primary")
        )
        info_label.pack(anchor="e", pady=2)
    
    # أزرار الاختبار
    buttons_frame = tk.Frame(main_frame, bg=modern_styles.get_color("background"))
    buttons_frame.pack(fill="x", pady=20)
    
    def test_input():
        """اختبار إدخال النص"""
        text_value = text_entry.get()
        combo_value = combo_box.get()
        textarea_value = textarea.get("1.0", "end-1c")
        
        result = f"النص المدخل: {text_value}\nالمدينة المختارة: {combo_value}\nالوصف: {textarea_value}"
        
        # إنشاء نافذة النتيجة
        result_window = tk.Toplevel(root)
        result_window.title("نتيجة الاختبار")
        result_window.geometry("400x300")
        result_window.configure(bg=modern_styles.get_color("background"))
        
        result_text = modern_styles.create_rtl_text(
            result_window,
            font=modern_styles.get_safe_font("body")
        )
        result_text.pack(fill="both", expand=True, padx=20, pady=20)
        result_text.insert("1.0", result)
    
    def close_app():
        """إغلاق التطبيق"""
        root.destroy()
    
    # زر الاختبار
    test_button = tk.Button(
        buttons_frame,
        text="🧪 اختبار الإدخال",
        font=modern_styles.get_safe_font("button"),
        bg=modern_styles.get_color("primary"),
        fg=modern_styles.get_color("text_white"),
        relief="flat",
        padx=20,
        pady=10,
        command=test_input
    )
    test_button.pack(side="right", padx=10)
    
    # زر الإغلاق
    close_button = tk.Button(
        buttons_frame,
        text="❌ إغلاق",
        font=modern_styles.get_safe_font("button"),
        bg=modern_styles.get_color("danger"),
        fg=modern_styles.get_color("text_white"),
        relief="flat",
        padx=20,
        pady=10,
        command=close_app
    )
    close_button.pack(side="right", padx=10)
    
    # تشغيل التطبيق
    root.mainloop()

if __name__ == "__main__":
    print("🧪 بدء اختبار دعم RTL للغة العربية...")
    test_rtl_components()
    print("✅ انتهى الاختبار")
