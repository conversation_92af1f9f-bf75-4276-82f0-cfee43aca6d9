"""
مدير البيانات لنظام دراسة الجدوى
يوفر وظائف الحفظ والتحميل والتصدير والنسخ الاحتياطي
"""

import json
import os
import shutil
import datetime
from pathlib import Path
from .models import ProjectData

class DataManager:
    """مدير البيانات الرئيسي"""
    
    def __init__(self):
        self.data_folder = Path("data/projects")
        self.backup_folder = Path("data/backups")
        self.templates_folder = Path("data/templates")
        
        # إنشاء المجلدات إذا لم تكن موجودة
        for folder in [self.data_folder, self.backup_folder, self.templates_folder]:
            folder.mkdir(parents=True, exist_ok=True)
    
    def save_project(self, project_data: ProjectData, filename: str = None):
        """حفظ مشروع"""
        try:
            if not filename:
                # إنشاء اسم ملف تلقائي
                project_name = project_data.project_info.get("اسم المشروع", "مشروع_جديد")
                # تنظيف اسم الملف من الأحرف غير المسموحة
                safe_name = "".join(c for c in project_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"{safe_name}_{timestamp}.json"
            
            filepath = self.data_folder / filename
            
            # حفظ البيانات
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(project_data.to_dict(), f, ensure_ascii=False, indent=2)
            
            # إنشاء نسخة احتياطية
            self.create_backup(filepath)
            
            return str(filepath)
            
        except Exception as e:
            raise Exception(f"فشل في حفظ المشروع: {str(e)}")
    
    def load_project(self, filepath: str):
        """تحميل مشروع"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            project_data = ProjectData()
            project_data.from_dict(data)
            
            return project_data
            
        except Exception as e:
            raise Exception(f"فشل في تحميل المشروع: {str(e)}")
    
    def create_backup(self, filepath: Path):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"{filepath.stem}_backup_{timestamp}.json"
            backup_path = self.backup_folder / backup_name
            
            shutil.copy2(filepath, backup_path)
            
            # الاحتفاظ بآخر 10 نسخ احتياطية فقط
            self.cleanup_old_backups()
            
        except Exception as e:
            print(f"تحذير: فشل في إنشاء نسخة احتياطية: {str(e)}")
    
    def cleanup_old_backups(self, max_backups: int = 10):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            backup_files = list(self.backup_folder.glob("*.json"))
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # حذف النسخ الزائدة
            for old_backup in backup_files[max_backups:]:
                old_backup.unlink()
                
        except Exception as e:
            print(f"تحذير: فشل في تنظيف النسخ الاحتياطية: {str(e)}")
    
    def list_projects(self):
        """قائمة بجميع المشاريع المحفوظة"""
        try:
            projects = []
            for filepath in self.data_folder.glob("*.json"):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    project_info = {
                        'filename': filepath.name,
                        'filepath': str(filepath),
                        'project_name': data.get('project_info', {}).get('اسم المشروع', 'غير محدد'),
                        'created_date': data.get('metadata', {}).get('created_date', ''),
                        'last_modified': data.get('metadata', {}).get('last_modified', ''),
                        'file_size': filepath.stat().st_size
                    }
                    projects.append(project_info)
                    
                except Exception:
                    continue  # تجاهل الملفات التالفة
            
            # ترتيب حسب تاريخ آخر تعديل
            projects.sort(key=lambda x: x['last_modified'], reverse=True)
            return projects
            
        except Exception as e:
            raise Exception(f"فشل في قراءة قائمة المشاريع: {str(e)}")
    
    def export_to_text(self, project_data: ProjectData, filepath: str):
        """تصدير المشروع إلى ملف نصي"""
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("تقرير دراسة الجدوى الشامل\n")
                f.write("=" * 50 + "\n\n")
                
                # معلومات المشروع
                f.write("معلومات المشروع:\n")
                f.write("-" * 20 + "\n")
                for key, value in project_data.project_info.items():
                    if value:
                        f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # المعلومات الشخصية
                f.write("المعلومات الشخصية:\n")
                f.write("-" * 20 + "\n")
                for key, value in project_data.personal_info.items():
                    if value:
                        f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # تحليل SWOT
                f.write("تحليل SWOT:\n")
                f.write("-" * 20 + "\n")
                for key, value in project_data.swot.items():
                    if value:
                        f.write(f"{key}:\n{value}\n\n")
                
                # المزيج التسويقي
                f.write("المزيج التسويقي:\n")
                f.write("-" * 20 + "\n")
                for key, value in project_data.marketing_mix.items():
                    if value:
                        f.write(f"{key}:\n{value}\n\n")
                
                # الملخص المالي
                f.write("الملخص المالي:\n")
                f.write("-" * 20 + "\n")
                for key, value in project_data.summary.items():
                    if value:
                        f.write(f"{key}: {value}\n")
                f.write("\n")
                
                # إحصائيات
                stats = project_data.get_summary_statistics()
                f.write("الإحصائيات:\n")
                f.write("-" * 20 + "\n")
                f.write(f"نسبة اكتمال المشروع: {stats.get('completion_rate', 0):.1f}%\n")
                f.write(f"الأقسام المكتملة: {stats.get('completed_sections', 0)} من {stats.get('total_sections', 0)}\n")
                if 'total_investment' in stats:
                    f.write(f"إجمالي الاستثمار المطلوب: {stats['total_investment']:,.2f} ريال\n")
                
                f.write(f"\nتاريخ التصدير: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            return True
            
        except Exception as e:
            raise Exception(f"فشل في تصدير المشروع: {str(e)}")
    
    def create_template(self, project_data: ProjectData, template_name: str):
        """إنشاء قالب من مشروع موجود"""
        try:
            # إنشاء نسخة من البيانات وتنظيفها
            template_data = project_data.to_dict()
            
            # مسح البيانات الشخصية والمالية المحددة
            template_data['personal_info'] = {
                key: "" for key in template_data['personal_info'].keys()
            }
            
            # الاحتفاظ بالهيكل فقط للبيانات المالية
            if 'financials' in template_data:
                for section in template_data['financials']:
                    if isinstance(template_data['financials'][section], list):
                        template_data['financials'][section] = []
            
            # تحديث معلومات القالب
            template_data['metadata'] = {
                'template_name': template_name,
                'created_date': datetime.datetime.now().isoformat(),
                'version': '1.0',
                'is_template': True
            }
            
            # حفظ القالب
            template_path = self.templates_folder / f"{template_name}.json"
            with open(template_path, 'w', encoding='utf-8') as f:
                json.dump(template_data, f, ensure_ascii=False, indent=2)
            
            return str(template_path)
            
        except Exception as e:
            raise Exception(f"فشل في إنشاء القالب: {str(e)}")
    
    def load_template(self, template_name: str):
        """تحميل قالب"""
        try:
            template_path = self.templates_folder / f"{template_name}.json"
            
            with open(template_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            project_data = ProjectData()
            project_data.from_dict(data)
            
            # إعادة تعيين معلومات البيانات الوصفية
            project_data.metadata['is_template'] = False
            project_data.metadata['created_date'] = datetime.datetime.now().isoformat()
            project_data.metadata['last_modified'] = ""
            
            return project_data
            
        except Exception as e:
            raise Exception(f"فشل في تحميل القالب: {str(e)}")
    
    def list_templates(self):
        """قائمة بجميع القوالب المتاحة"""
        try:
            templates = []
            for filepath in self.templates_folder.glob("*.json"):
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    template_info = {
                        'name': filepath.stem,
                        'filepath': str(filepath),
                        'template_name': data.get('metadata', {}).get('template_name', filepath.stem),
                        'created_date': data.get('metadata', {}).get('created_date', ''),
                        'file_size': filepath.stat().st_size
                    }
                    templates.append(template_info)
                    
                except Exception:
                    continue
            
            templates.sort(key=lambda x: x['created_date'], reverse=True)
            return templates
            
        except Exception as e:
            raise Exception(f"فشل في قراءة قائمة القوالب: {str(e)}")
    
    def delete_project(self, filepath: str):
        """حذف مشروع"""
        try:
            os.remove(filepath)
            return True
        except Exception as e:
            raise Exception(f"فشل في حذف المشروع: {str(e)}")
    
    def get_project_statistics(self):
        """إحصائيات عامة عن المشاريع"""
        try:
            projects = self.list_projects()
            templates = self.list_templates()
            backups = list(self.backup_folder.glob("*.json"))
            
            total_size = sum(p['file_size'] for p in projects)
            
            return {
                'total_projects': len(projects),
                'total_templates': len(templates),
                'total_backups': len(backups),
                'total_size_mb': total_size / (1024 * 1024),
                'last_project_date': projects[0]['last_modified'] if projects else None
            }
            
        except Exception as e:
            return {'error': str(e)}
