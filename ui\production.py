import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import modern_styles
from tkinter import simpledialog
from data.models import EquipmentItem, RawMaterial

class ProductionFrame(tk.Frame):
    """واجهة مستلزمات الإنتاج"""

    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.setup_ui()

    def setup_ui(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg="#ffffff")
        title_frame.pack(fill="x", pady=(0, 20))

        tk.Label(title_frame, text="مستلزمات الإنتاج",
                font=("Tajawal", 18, "bold"), bg="#ffffff", fg="#1e3a8a").pack()

        # خط فاصل
        separator = tk.Frame(title_frame, height=2, bg="#4a90e2")
        separator.pack(fill="x", pady=10)

        # إطار قابل للتمرير
        canvas = tk.Canvas(self, bg="#ffffff")
        scrollbar = ttk.Scrollbar(self, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # المحتوى الرئيسي
        main_form = tk.Frame(scrollable_frame, bg="#ffffff")
        main_form.pack(fill="both", expand=True, padx=40, pady=20)

        # قسم المعدات والآلات والأثاث
        self.create_equipment_section(main_form)

        # قسم المواد الخام
        self.create_raw_materials_section(main_form)

        # قسم الملخص المالي
        self.create_financial_summary(main_form)

        # أزرار العمليات
        buttons_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20, padx=40)

        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat",
                    "padx": 20, "pady": 8, "cursor": "hand2"}

        tk.Button(buttons_frame, text="💾 حفظ البيانات", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="🧮 حساب التكاليف", bg="#3498db", fg="white",
                 command=self.calculate_totals, **btn_style).pack(side="right", padx=10)

    def create_equipment_section(self, parent):
        """إنشاء قسم المعدات والآلات والأثاث"""
        equipment_section = tk.LabelFrame(parent, text="🔧 المعدات والآلات والأثاث",
                                        font=("Tajawal", 12, "bold"), bg="#ffffff",
                                        fg="#2c3e50", relief="solid", bd=1)
        equipment_section.pack(fill="x", pady=10)

        # أزرار إدارة المعدات
        equip_buttons_frame = tk.Frame(equipment_section, bg="#ffffff")
        equip_buttons_frame.pack(fill="x", padx=10, pady=5)

        btn_style = {"font": ("Tajawal", 10), "relief": "flat", "padx": 15, "pady": 5}

        tk.Button(equip_buttons_frame, text="➕ إضافة معدة", bg="#27ae60", fg="white",
                 command=self.add_equipment, **btn_style).pack(side="right", padx=5)

        tk.Button(equip_buttons_frame, text="✏️ تعديل", bg="#f39c12", fg="white",
                 command=self.edit_equipment, **btn_style).pack(side="right", padx=5)

        tk.Button(equip_buttons_frame, text="🗑️ حذف", bg="#e74c3c", fg="white",
                 command=self.delete_equipment, **btn_style).pack(side="right", padx=5)

        # جدول المعدات
        equip_frame = tk.Frame(equipment_section, bg="#ffffff")
        equip_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # تعريف الأعمدة
        columns = ("البند", "العدد", "سعر الوحدة", "الإجمالي")
        self.equip_tree = ttk.Treeview(equip_frame, columns=columns, show="headings", height=6)

        # تكوين الأعمدة
        for col in columns:
            self.equip_tree.heading(col, text=col)
            if col == "البند":
                self.equip_tree.column(col, width=200, anchor="e")
            else:
                self.equip_tree.column(col, width=120, anchor="center")

        # شريط التمرير للجدول
        equip_scrollbar = ttk.Scrollbar(equip_frame, orient="vertical", command=self.equip_tree.yview)
        self.equip_tree.configure(yscrollcommand=equip_scrollbar.set)

        self.equip_tree.pack(side="right", fill="both", expand=True)
        equip_scrollbar.pack(side="left", fill="y")

        # إجمالي المعدات
        equip_total_frame = tk.Frame(equipment_section, bg="#ffffff")
        equip_total_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(equip_total_frame, text="إجمالي تكلفة المعدات:",
                font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(side="right", padx=10)

        self.equip_total_label = tk.Label(equip_total_frame, text="0.00",
                                        font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#27ae60")
        self.equip_total_label.pack(side="right")

    def create_raw_materials_section(self, parent):
        """إنشاء قسم المواد الخام"""
        materials_section = tk.LabelFrame(parent, text="📦 المواد الخام (شهرياً)",
                                        font=("Tajawal", 12, "bold"), bg="#ffffff",
                                        fg="#2c3e50", relief="solid", bd=1)
        materials_section.pack(fill="x", pady=10)

        # أزرار إدارة المواد الخام
        materials_buttons_frame = tk.Frame(materials_section, bg="#ffffff")
        materials_buttons_frame.pack(fill="x", padx=10, pady=5)

        btn_style = {"font": ("Tajawal", 10), "relief": "flat", "padx": 15, "pady": 5}

        tk.Button(materials_buttons_frame, text="➕ إضافة مادة", bg="#27ae60", fg="white",
                 command=self.add_raw_material, **btn_style).pack(side="right", padx=5)

        tk.Button(materials_buttons_frame, text="✏️ تعديل", bg="#f39c12", fg="white",
                 command=self.edit_raw_material, **btn_style).pack(side="right", padx=5)

        tk.Button(materials_buttons_frame, text="🗑️ حذف", bg="#e74c3c", fg="white",
                 command=self.delete_raw_material, **btn_style).pack(side="right", padx=5)

        # جدول المواد الخام
        materials_frame = tk.Frame(materials_section, bg="#ffffff")
        materials_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # تعريف الأعمدة
        columns = ("المادة", "الكمية", "سعر الوحدة", "الإجمالي الشهري")
        self.materials_tree = ttk.Treeview(materials_frame, columns=columns, show="headings", height=6)

        # تكوين الأعمدة
        for col in columns:
            self.materials_tree.heading(col, text=col)
            if col == "المادة":
                self.materials_tree.column(col, width=200, anchor="e")
            else:
                self.materials_tree.column(col, width=120, anchor="center")

        # شريط التمرير للجدول
        materials_scrollbar = ttk.Scrollbar(materials_frame, orient="vertical", command=self.materials_tree.yview)
        self.materials_tree.configure(yscrollcommand=materials_scrollbar.set)

        self.materials_tree.pack(side="right", fill="both", expand=True)
        materials_scrollbar.pack(side="left", fill="y")

        # إجمالي المواد الخام
        materials_total_frame = tk.Frame(materials_section, bg="#ffffff")
        materials_total_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(materials_total_frame, text="إجمالي تكلفة المواد الخام الشهرية:",
                font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(side="right", padx=10)

        self.materials_total_label = tk.Label(materials_total_frame, text="0.00",
                                            font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#e74c3c")
        self.materials_total_label.pack(side="right")

    def create_financial_summary(self, parent):
        """إنشاء قسم الملخص المالي"""
        summary_section = tk.LabelFrame(parent, text="💰 الملخص المالي",
                                      font=("Tajawal", 12, "bold"), bg="#ffffff",
                                      fg="#2c3e50", relief="solid", bd=1)
        summary_section.pack(fill="x", pady=10)

        # إطار الملخص
        summary_frame = tk.Frame(summary_section, bg="#f8f9fa", relief="solid", bd=1)
        summary_frame.pack(fill="x", padx=10, pady=10)

        # تكلفة المعدات
        equip_summary_frame = tk.Frame(summary_frame, bg="#f8f9fa")
        equip_summary_frame.pack(fill="x", padx=15, pady=5)

        tk.Label(equip_summary_frame, text="إجمالي تكلفة المعدات (مرة واحدة):",
                font=("Tajawal", 11), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)

        self.summary_equip_label = tk.Label(equip_summary_frame, text="0.00 ريال",
                                          font=("Tajawal", 11, "bold"), bg="#f8f9fa", fg="#27ae60")
        self.summary_equip_label.pack(side="right")

        # تكلفة المواد الخام
        materials_summary_frame = tk.Frame(summary_frame, bg="#f8f9fa")
        materials_summary_frame.pack(fill="x", padx=15, pady=5)

        tk.Label(materials_summary_frame, text="إجمالي تكلفة المواد الخام (شهرياً):",
                font=("Tajawal", 11), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)

        self.summary_materials_label = tk.Label(materials_summary_frame, text="0.00 ريال",
                                              font=("Tajawal", 11, "bold"), bg="#f8f9fa", fg="#e74c3c")
        self.summary_materials_label.pack(side="right")

        # التكلفة السنوية للمواد الخام
        yearly_materials_frame = tk.Frame(summary_frame, bg="#f8f9fa")
        yearly_materials_frame.pack(fill="x", padx=15, pady=5)

        tk.Label(yearly_materials_frame, text="تكلفة المواد الخام السنوية:",
                font=("Tajawal", 11), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)

        self.yearly_materials_label = tk.Label(yearly_materials_frame, text="0.00 ريال",
                                             font=("Tajawal", 11, "bold"), bg="#f8f9fa", fg="#9b59b6")
        self.yearly_materials_label.pack(side="right")

    def add_equipment(self):
        """إضافة معدة جديدة"""
        dialog = EquipmentDialog(self, "إضافة معدة جديدة")
        if dialog.result:
            name, quantity, unit_price = dialog.result
            total = quantity * unit_price

            # تنسيق العملة المزدوجة
            unit_price_formatted = modern_styles.format_currency_dual(unit_price)
            total_formatted = modern_styles.format_currency_dual(total)

            self.equip_tree.insert("", "end", values=(name, quantity, unit_price_formatted, total_formatted))
            self.calculate_totals()

    def edit_equipment(self):
        """تعديل معدة محددة"""
        selected = self.equip_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار معدة للتعديل")
            return

        item = selected[0]
        values = self.equip_tree.item(item, "values")

        dialog = EquipmentDialog(self, "تعديل المعدة",
                               initial_values=(values[0], int(values[1]), float(values[2].replace(",", ""))))
        if dialog.result:
            name, quantity, unit_price = dialog.result
            total = quantity * unit_price

            # تنسيق العملة المزدوجة
            unit_price_formatted = modern_styles.format_currency_dual(unit_price)
            total_formatted = modern_styles.format_currency_dual(total)

            self.equip_tree.item(item, values=(name, quantity, unit_price_formatted, total_formatted))
            self.calculate_totals()

    def delete_equipment(self):
        """حذف معدة محددة"""
        selected = self.equip_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار معدة للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه المعدة؟"):
            self.equip_tree.delete(selected[0])
            self.calculate_totals()

    def add_raw_material(self):
        """إضافة مادة خام جديدة"""
        dialog = RawMaterialDialog(self, "إضافة مادة خام جديدة")
        if dialog.result:
            name, quantity, unit_price = dialog.result
            total = quantity * unit_price

            # تنسيق العملة المزدوجة
            unit_price_formatted = modern_styles.format_currency_dual(unit_price)
            total_formatted = modern_styles.format_currency_dual(total)

            self.materials_tree.insert("", "end", values=(name, quantity, unit_price_formatted, total_formatted))
            self.calculate_totals()

    def edit_raw_material(self):
        """تعديل مادة خام محددة"""
        selected = self.materials_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مادة للتعديل")
            return

        item = selected[0]
        values = self.materials_tree.item(item, "values")

        dialog = RawMaterialDialog(self, "تعديل المادة الخام",
                                 initial_values=(values[0], int(values[1]), float(values[2].replace(",", ""))))
        if dialog.result:
            name, quantity, unit_price = dialog.result
            total = quantity * unit_price

            # تنسيق العملة المزدوجة
            unit_price_formatted = modern_styles.format_currency_dual(unit_price)
            total_formatted = modern_styles.format_currency_dual(total)

            self.materials_tree.item(item, values=(name, quantity, unit_price_formatted, total_formatted))
            self.calculate_totals()

    def delete_raw_material(self):
        """حذف مادة خام محددة"""
        selected = self.materials_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مادة للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه المادة؟"):
            self.materials_tree.delete(selected[0])
            self.calculate_totals()

    def calculate_totals(self):
        """حساب إجمالي التكاليف مع عرض العملة المزدوجة"""
        # حساب إجمالي المعدات
        equip_total = 0
        for item in self.equip_tree.get_children():
            values = self.equip_tree.item(item, "values")
            # استخراج القيمة الرقمية من النص المنسق
            total_text = values[3]
            if "د.ع" in total_text:
                # استخراج الرقم قبل "د.ع"
                amount_str = total_text.split("د.ع")[0].replace(",", "").strip()
                equip_total += float(amount_str)

        # حساب إجمالي المواد الخام الشهرية
        materials_total = 0
        for item in self.materials_tree.get_children():
            values = self.materials_tree.item(item, "values")
            # استخراج القيمة الرقمية من النص المنسق
            total_text = values[3]
            if "د.ع" in total_text:
                # استخراج الرقم قبل "د.ع"
                amount_str = total_text.split("د.ع")[0].replace(",", "").strip()
                materials_total += float(amount_str)

        # تحديث التسميات بالعملة المزدوجة
        self.equip_total_label.config(text=modern_styles.format_currency_dual(equip_total))
        self.materials_total_label.config(text=modern_styles.format_currency_dual(materials_total))

        # تحديث الملخص بالعملة المزدوجة
        self.summary_equip_label.config(text=modern_styles.format_currency_dual(equip_total))
        self.summary_materials_label.config(text=modern_styles.format_currency_dual(materials_total))
        self.yearly_materials_label.config(text=modern_styles.format_currency_dual(materials_total * 12))

    def save_data(self):
        """حفظ البيانات في نموذج البيانات والانتقال للقسم التالي"""
        # حفظ المعدات
        equipment_list = []
        for item in self.equip_tree.get_children():
            values = self.equip_tree.item(item, "values")
            # استخراج القيم الرقمية من النص المنسق
            unit_price_text = values[2]
            total_price_text = values[3]

            if "د.ع" in unit_price_text:
                unit_price = float(unit_price_text.split("د.ع")[0].replace(",", "").strip())
            else:
                unit_price = float(values[2].replace(",", ""))

            if "د.ع" in total_price_text:
                total_price = float(total_price_text.split("د.ع")[0].replace(",", "").strip())
            else:
                total_price = float(values[3].replace(",", ""))

            equipment_list.append({
                "name": values[0],
                "quantity": int(values[1]),
                "unit_price": unit_price,
                "total_price": total_price
            })

        # حفظ المواد الخام
        materials_list = []
        for item in self.materials_tree.get_children():
            values = self.materials_tree.item(item, "values")
            # استخراج القيم الرقمية من النص المنسق
            unit_price_text = values[2]
            total_price_text = values[3]

            if "د.ع" in unit_price_text:
                unit_price = float(unit_price_text.split("د.ع")[0].replace(",", "").strip())
            else:
                unit_price = float(values[2].replace(",", ""))

            if "د.ع" in total_price_text:
                total_price = float(total_price_text.split("د.ع")[0].replace(",", "").strip())
            else:
                total_price = float(values[3].replace(",", ""))

            materials_list.append({
                "name": values[0],
                "quantity": int(values[1]),
                "unit_price": unit_price,
                "total_price": total_price
            })

        self.project_data.production["equipment"] = equipment_list
        self.project_data.production["raw_materials"] = materials_list

        # الانتقال إلى القسم التالي
        parent = self.master
        while parent and not hasattr(parent, 'go_to_next_section'):
            parent = parent.master
        if parent and hasattr(parent, 'go_to_next_section'):
            parent.go_to_next_section()

    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        # مسح البيانات الحالية
        for item in self.equip_tree.get_children():
            self.equip_tree.delete(item)
        for item in self.materials_tree.get_children():
            self.materials_tree.delete(item)

        # تحميل المعدات مع تنسيق العملة المزدوجة
        equipment_list = self.project_data.production.get("equipment", [])
        for equipment in equipment_list:
            unit_price_formatted = modern_styles.format_currency_dual(equipment['unit_price'])
            total_formatted = modern_styles.format_currency_dual(equipment['total_price'])

            self.equip_tree.insert("", "end", values=(
                equipment["name"],
                equipment["quantity"],
                unit_price_formatted,
                total_formatted
            ))

        # تحميل المواد الخام مع تنسيق العملة المزدوجة
        materials_list = self.project_data.production.get("raw_materials", [])
        for material in materials_list:
            unit_price_formatted = modern_styles.format_currency_dual(material['unit_price'])
            total_formatted = modern_styles.format_currency_dual(material['total_price'])

            self.materials_tree.insert("", "end", values=(
                material["name"],
                material["quantity"],
                unit_price_formatted,
                total_formatted
            ))

        # إعادة حساب الإجماليات
        self.calculate_totals()

    def clear_data(self):
        """مسح جميع البيانات"""
        # مسح الجداول
        for item in self.equip_tree.get_children():
            self.equip_tree.delete(item)
        for item in self.materials_tree.get_children():
            self.materials_tree.delete(item)

        # إعادة تعيين الإجماليات
        self.equip_total_label.config(text="0.00")
        self.materials_total_label.config(text="0.00")
        self.summary_equip_label.config(text="0.00 ريال")
        self.summary_materials_label.config(text="0.00 ريال")
        self.yearly_materials_label.config(text="0.00 ريال")

        # مسح البيانات من النموذج
        self.project_data.production["equipment"] = []
        self.project_data.production["raw_materials"] = []


class EquipmentDialog:
    """حوار إضافة/تعديل المعدات"""

    def __init__(self, parent, title, initial_values=None):
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.configure(bg="#ffffff")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        # العنوان
        tk.Label(self.dialog, text=title, font=("Tajawal", 14, "bold"),
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        # النموذج
        form_frame = tk.Frame(self.dialog, bg="#ffffff")
        form_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # اسم المعدة
        tk.Label(form_frame, text="اسم المعدة:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.name_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="right")
        self.name_entry.pack(fill="x", pady=5)

        # العدد
        tk.Label(form_frame, text="العدد:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.quantity_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.quantity_entry.pack(fill="x", pady=5)

        # سعر الوحدة
        tk.Label(form_frame, text="سعر الوحدة:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.price_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.price_entry.pack(fill="x", pady=5)

        # تعبئة القيم الأولية
        if initial_values:
            self.name_entry.insert(0, initial_values[0])
            self.quantity_entry.insert(0, str(initial_values[1]))
            self.price_entry.insert(0, str(initial_values[2]))

        # الأزرار
        buttons_frame = tk.Frame(self.dialog, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)

        tk.Button(buttons_frame, text="حفظ", font=("Tajawal", 11), bg="#27ae60", fg="white",
                 command=self.save, padx=20, pady=5).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="إلغاء", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=self.cancel, padx=20, pady=5).pack(side="right", padx=10)

        # التركيز على الحقل الأول
        self.name_entry.focus()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def save(self):
        """حفظ البيانات"""
        try:
            name = self.name_entry.get().strip()
            quantity = int(self.quantity_entry.get())
            unit_price = float(self.price_entry.get())

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المعدة")
                return

            if quantity <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال عدد صحيح أكبر من صفر")
                return

            if unit_price <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح أكبر من صفر")
                return

            self.result = (name, quantity, unit_price)
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للعدد والسعر")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()


class RawMaterialDialog:
    """حوار إضافة/تعديل المواد الخام"""

    def __init__(self, parent, title, initial_values=None):
        self.result = None

        # إنشاء النافذة
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.configure(bg="#ffffff")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # توسيط النافذة
        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        # العنوان
        tk.Label(self.dialog, text=title, font=("Tajawal", 14, "bold"),
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        # النموذج
        form_frame = tk.Frame(self.dialog, bg="#ffffff")
        form_frame.pack(fill="both", expand=True, padx=30, pady=20)

        # اسم المادة
        tk.Label(form_frame, text="اسم المادة الخام:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.name_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="right")
        self.name_entry.pack(fill="x", pady=5)

        # الكمية
        tk.Label(form_frame, text="الكمية الشهرية:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.quantity_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.quantity_entry.pack(fill="x", pady=5)

        # سعر الوحدة
        tk.Label(form_frame, text="سعر الوحدة:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.price_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.price_entry.pack(fill="x", pady=5)

        # تعبئة القيم الأولية
        if initial_values:
            self.name_entry.insert(0, initial_values[0])
            self.quantity_entry.insert(0, str(initial_values[1]))
            self.price_entry.insert(0, str(initial_values[2]))

        # الأزرار
        buttons_frame = tk.Frame(self.dialog, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)

        tk.Button(buttons_frame, text="حفظ", font=("Tajawal", 11), bg="#27ae60", fg="white",
                 command=self.save, padx=20, pady=5).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="إلغاء", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=self.cancel, padx=20, pady=5).pack(side="right", padx=10)

        # التركيز على الحقل الأول
        self.name_entry.focus()

        # انتظار إغلاق النافذة
        self.dialog.wait_window()

    def save(self):
        """حفظ البيانات"""
        try:
            name = self.name_entry.get().strip()
            quantity = int(self.quantity_entry.get())
            unit_price = float(self.price_entry.get())

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المادة الخام")
                return

            if quantity <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة أكبر من صفر")
                return

            if unit_price <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح أكبر من صفر")
                return

            self.result = (name, quantity, unit_price)
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للكمية والسعر")

    def cancel(self):
        """إلغاء العملية"""
        self.dialog.destroy()