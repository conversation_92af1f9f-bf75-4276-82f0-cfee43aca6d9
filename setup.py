#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف الإعداد لنظام دراسة الجدوى الشامل
"""

from setuptools import setup, find_packages
import os

# قراءة ملف README
def read_readme():
    with open("README.md", "r", encoding="utf-8") as f:
        return f.read()

# قراءة المتطلبات
def read_requirements():
    with open("requirements.txt", "r", encoding="utf-8") as f:
        return [line.strip() for line in f if line.strip() and not line.startswith("#")]

setup(
    name="feasibility-study-system",
    version="1.0.0",
    description="نظام شامل لإعداد دراسات الجدوى الاقتصادية باللغة العربية",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    author="نظام دراسة الجدوى",
    author_email="<EMAIL>",
    url="https://github.com/feasibility-study-system/saif",
    packages=find_packages(),
    include_package_data=True,
    install_requires=read_requirements(),
    python_requires=">=3.7",
    classifiers=[
        "Development Status :: 5 - Production/Stable",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial",
        "Topic :: Scientific/Engineering",
        "Natural Language :: Arabic",
    ],
    keywords="feasibility study, business plan, financial analysis, arabic, rtl",
    entry_points={
        "console_scripts": [
            "feasibility-study=main:main",
        ],
    },
    package_data={
        "": ["*.md", "*.txt", "*.json"],
        "assets": ["*"],
        "ui": ["*.py"],
        "data": ["*.py"],
    },
    zip_safe=False,
)
