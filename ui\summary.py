import tkinter as tk
from tkinter import ttk, messagebox

class SummaryFrame(tk.Frame):
    """واجهة ملخص الربح السنوي"""
    
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg="#ffffff")
        title_frame.pack(fill="x", pady=(0, 20))
        
        tk.Label(title_frame, text="ملخص الربح السنوي", 
                font=("Tajawal", 18, "bold"), bg="#ffffff", fg="#1e3a8a").pack()
        
        # خط فاصل
        separator = tk.Frame(title_frame, height=2, bg="#4a90e2")
        separator.pack(fill="x", pady=10)
        
        # إطار النموذج الرئيسي
        main_form = tk.Frame(self, bg="#ffffff")
        main_form.pack(fill="both", expand=True, padx=40)
        
        # قسم الملخص المالي التلقائي
        auto_section = tk.LabelFrame(main_form, text="📊 الملخص المالي التلقائي", 
                                   font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                   fg="#2c3e50", relief="solid", bd=1)
        auto_section.pack(fill="x", pady=10)
        
        # إطار البيانات التلقائية
        auto_frame = tk.Frame(auto_section, bg="#f8f9fa", relief="solid", bd=1)
        auto_frame.pack(fill="x", padx=10, pady=10)
        
        # إجمالي الإيرادات
        revenue_frame = tk.Frame(auto_frame, bg="#f8f9fa")
        revenue_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(revenue_frame, text="إجمالي الإيرادات السنوية:", 
                font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)
        
        self.auto_revenue_label = tk.Label(revenue_frame, text="0.00 ريال", 
                                         font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#27ae60")
        self.auto_revenue_label.pack(side="right")
        
        # إجمالي التكاليف
        costs_frame = tk.Frame(auto_frame, bg="#f8f9fa")
        costs_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(costs_frame, text="إجمالي التكاليف السنوية:", 
                font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)
        
        self.auto_costs_label = tk.Label(costs_frame, text="0.00 ريال", 
                                       font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#e74c3c")
        self.auto_costs_label.pack(side="right")
        
        # قيمة إهلاك الأصول
        depreciation_frame = tk.Frame(auto_frame, bg="#f8f9fa")
        depreciation_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(depreciation_frame, text="قيمة إهلاك الأصول (10% سنوياً):", 
                font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)
        
        self.auto_depreciation_label = tk.Label(depreciation_frame, text="0.00 ريال", 
                                              font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#f39c12")
        self.auto_depreciation_label.pack(side="right")
        
        # صافي الأرباح
        profit_frame = tk.Frame(auto_frame, bg="#f8f9fa")
        profit_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(profit_frame, text="صافي الأرباح السنوية:", 
                font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=10)
        
        self.auto_profit_label = tk.Label(profit_frame, text="0.00 ريال", 
                                        font=("Tajawal", 12, "bold"), bg="#f8f9fa", fg="#3498db")
        self.auto_profit_label.pack(side="right")
        
        # زر التحديث التلقائي
        update_button = tk.Button(auto_section, text="🔄 تحديث البيانات التلقائية", 
                                font=("Tajawal", 11, "bold"), bg="#3498db", fg="white",
                                relief="flat", padx=20, pady=8, cursor="hand2",
                                command=self.update_auto_data)
        update_button.pack(pady=10)
        
        # قسم الإدخال اليدوي
        manual_section = tk.LabelFrame(main_form, text="✏️ الإدخال اليدوي (اختياري)", 
                                     font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                     fg="#2c3e50", relief="solid", bd=1)
        manual_section.pack(fill="x", pady=10)
        
        # الحقول اليدوية
        manual_fields = [
            "إجمالي الإيرادات",
            "إجمالي التكاليف السنوية",
            "قيمة إهلاك الأصول",
            "صافي الأرباح"
        ]
        
        for field in manual_fields:
            field_frame = tk.Frame(manual_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field + ":", font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50", width=25, anchor="e").pack(side="right", padx=(0, 10))
            
            entry = tk.Entry(field_frame, font=("Tajawal", 11), width=30, 
                           justify="right", relief="solid", bd=1)
            entry.pack(side="right", fill="x", expand=True)
            entry.bind("<KeyRelease>", lambda e: self.format_currency(e))
            self.entries[field] = entry
        
        # قسم التحليل والمؤشرات
        analysis_section = tk.LabelFrame(main_form, text="📈 التحليل والمؤشرات", 
                                       font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                       fg="#2c3e50", relief="solid", bd=1)
        analysis_section.pack(fill="x", pady=10)
        
        # إطار المؤشرات
        indicators_frame = tk.Frame(analysis_section, bg="#e8f4fd", relief="solid", bd=1)
        indicators_frame.pack(fill="x", padx=10, pady=10)
        
        # معدل العائد على الاستثمار
        roi_frame = tk.Frame(indicators_frame, bg="#e8f4fd")
        roi_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(roi_frame, text="معدل العائد على الاستثمار (ROI):", 
                font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#2c3e50").pack(side="right", padx=10)
        
        self.roi_label = tk.Label(roi_frame, text="--%", 
                                font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#3498db")
        self.roi_label.pack(side="right")
        
        # فترة الاسترداد
        payback_frame = tk.Frame(indicators_frame, bg="#e8f4fd")
        payback_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(payback_frame, text="فترة استرداد رأس المال:", 
                font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#2c3e50").pack(side="right", padx=10)
        
        self.payback_label = tk.Label(payback_frame, text="-- سنة", 
                                    font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#3498db")
        self.payback_label.pack(side="right")
        
        # هامش الربح
        margin_frame = tk.Frame(indicators_frame, bg="#e8f4fd")
        margin_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(margin_frame, text="هامش الربح الصافي:", 
                font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#2c3e50").pack(side="right", padx=10)
        
        self.margin_label = tk.Label(margin_frame, text="--%", 
                                   font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#3498db")
        self.margin_label.pack(side="right")
        
        # التقييم العام
        evaluation_frame = tk.Frame(indicators_frame, bg="#e8f4fd")
        evaluation_frame.pack(fill="x", padx=15, pady=5)
        
        tk.Label(evaluation_frame, text="التقييم العام للمشروع:", 
                font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#2c3e50").pack(side="right", padx=10)
        
        self.evaluation_label = tk.Label(evaluation_frame, text="غير محدد", 
                                       font=("Tajawal", 11, "bold"), bg="#e8f4fd", fg="#95a5a6")
        self.evaluation_label.pack(side="right")
        
        # قسم التوصيات
        recommendations_section = tk.LabelFrame(main_form, text="💡 التوصيات والملاحظات", 
                                               font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                               fg="#2c3e50", relief="solid", bd=1)
        recommendations_section.pack(fill="x", pady=10)
        
        # منطقة التوصيات
        recommendations_frame = tk.Frame(recommendations_section, bg="#ffffff")
        recommendations_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(recommendations_frame, text="التوصيات:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        self.recommendations_text = tk.Text(recommendations_frame, font=("Tajawal", 11), height=4, 
                                          relief="solid", bd=1, wrap="word")
        self.recommendations_text.pack(fill="x", pady=5)
        
        # الملاحظات
        notes_frame = tk.Frame(recommendations_section, bg="#ffffff")
        notes_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(notes_frame, text="ملاحظات إضافية:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        notes_text = tk.Text(notes_frame, font=("Tajawal", 11), height=3, 
                           relief="solid", bd=1, wrap="word")
        notes_text.pack(fill="x", pady=5)
        self.entries["ملاحظات إضافية"] = notes_text
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)
        
        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat", 
                    "padx": 20, "pady": 8, "cursor": "hand2"}
        
        tk.Button(buttons_frame, text="💾 حفظ الملخص", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🧮 حساب المؤشرات", bg="#3498db", fg="white",
                 command=self.calculate_indicators, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="📊 تقرير شامل", bg="#9b59b6", fg="white",
                 command=self.generate_report, **btn_style).pack(side="right", padx=10)
        
        # تحديث البيانات التلقائية عند التحميل
        self.update_auto_data()

    def update_auto_data(self):
        """تحديث البيانات التلقائية من الأقسام الأخرى"""
        try:
            # حساب الإيرادات السنوية من قسم الدراسة المالية
            yearly_revenues = 0
            if hasattr(self.project_data, 'financials') and 'yearly_revenues' in self.project_data.financials:
                for revenue in self.project_data.financials['yearly_revenues']:
                    yearly_revenues += revenue.get('total', 0)

            # حساب التكاليف السنوية
            annual_costs = 0
            if hasattr(self.project_data, 'financials'):
                # رأس المال العامل الشهري × 12
                if 'working_capital' in self.project_data.financials:
                    monthly_working = sum(item.get('amount', 0) for item in self.project_data.financials['working_capital'])
                    annual_costs += monthly_working * 12

                # تكاليف التأسيس (مرة واحدة)
                if 'startup_costs' in self.project_data.financials:
                    startup_costs = sum(item.get('amount', 0) for item in self.project_data.financials['startup_costs'])
                    annual_costs += startup_costs

            # حساب إهلاك الأصول (10% من رأس المال الثابت)
            depreciation = 0
            if hasattr(self.project_data, 'financials') and 'fixed_capital' in self.project_data.financials:
                fixed_capital = sum(item.get('amount', 0) for item in self.project_data.financials['fixed_capital'])
                depreciation = fixed_capital * 0.1  # 10% سنوياً

            # حساب صافي الأرباح
            net_profit = yearly_revenues - annual_costs - depreciation

            # تحديث التسميات
            self.auto_revenue_label.config(text=f"{yearly_revenues:,.2f} ريال")
            self.auto_costs_label.config(text=f"{annual_costs:,.2f} ريال")
            self.auto_depreciation_label.config(text=f"{depreciation:,.2f} ريال")
            self.auto_profit_label.config(text=f"{net_profit:,.2f} ريال")

            # تحديث لون صافي الأرباح
            if net_profit > 0:
                self.auto_profit_label.config(fg="#27ae60")
            elif net_profit < 0:
                self.auto_profit_label.config(fg="#e74c3c")
            else:
                self.auto_profit_label.config(fg="#95a5a6")

            # تحديث الحقول اليدوية
            self.entries["إجمالي الإيرادات"].delete(0, tk.END)
            self.entries["إجمالي الإيرادات"].insert(0, f"{yearly_revenues:,.2f}")

            self.entries["إجمالي التكاليف السنوية"].delete(0, tk.END)
            self.entries["إجمالي التكاليف السنوية"].insert(0, f"{annual_costs:,.2f}")

            self.entries["قيمة إهلاك الأصول"].delete(0, tk.END)
            self.entries["قيمة إهلاك الأصول"].insert(0, f"{depreciation:,.2f}")

            self.entries["صافي الأرباح"].delete(0, tk.END)
            self.entries["صافي الأرباح"].insert(0, f"{net_profit:,.2f}")

            # حساب المؤشرات تلقائياً
            self.calculate_indicators()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث البيانات التلقائية: {str(e)}")

    def format_currency(self, event):
        """تنسيق الأرقام المالية"""
        try:
            value = event.widget.get().replace(",", "")
            if value and value.replace(".", "").replace("-", "").isdigit():
                formatted = "{:,.2f}".format(float(value))
                event.widget.delete(0, tk.END)
                event.widget.insert(0, formatted)
        except:
            pass

    def calculate_indicators(self):
        """حساب المؤشرات المالية"""
        try:
            # الحصول على القيم
            revenue = float(self.entries["إجمالي الإيرادات"].get().replace(",", "") or 0)
            costs = float(self.entries["إجمالي التكاليف السنوية"].get().replace(",", "") or 0)
            depreciation = float(self.entries["قيمة إهلاك الأصول"].get().replace(",", "") or 0)
            net_profit = float(self.entries["صافي الأرباح"].get().replace(",", "") or 0)

            # حساب إجمالي الاستثمار
            total_investment = 0
            if hasattr(self.project_data, 'financials'):
                if 'startup_costs' in self.project_data.financials:
                    total_investment += sum(item.get('amount', 0) for item in self.project_data.financials['startup_costs'])
                if 'fixed_capital' in self.project_data.financials:
                    total_investment += sum(item.get('amount', 0) for item in self.project_data.financials['fixed_capital'])

            # حساب معدل العائد على الاستثمار (ROI)
            if total_investment > 0:
                roi = (net_profit / total_investment) * 100
                self.roi_label.config(text=f"{roi:.1f}%")

                # حساب فترة الاسترداد
                if net_profit > 0:
                    payback_years = total_investment / net_profit
                    self.payback_label.config(text=f"{payback_years:.1f} سنة")
                else:
                    self.payback_label.config(text="غير محدد")
            else:
                self.roi_label.config(text="غير محدد")
                self.payback_label.config(text="غير محدد")
                roi = 0

            # حساب هامش الربح الصافي
            if revenue > 0:
                profit_margin = (net_profit / revenue) * 100
                self.margin_label.config(text=f"{profit_margin:.1f}%")
            else:
                self.margin_label.config(text="غير محدد")
                profit_margin = 0

            # التقييم العام
            evaluation, color = self.evaluate_project(roi, profit_margin, net_profit)
            self.evaluation_label.config(text=evaluation, fg=color)

            # إنشاء التوصيات
            self.generate_recommendations(roi, profit_margin, net_profit, total_investment)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حساب المؤشرات: {str(e)}")

    def evaluate_project(self, roi, profit_margin, net_profit):
        """تقييم المشروع بناءً على المؤشرات"""
        if net_profit <= 0:
            return "غير مربح", "#e74c3c"
        elif roi >= 25 and profit_margin >= 20:
            return "ممتاز جداً", "#27ae60"
        elif roi >= 20 and profit_margin >= 15:
            return "ممتاز", "#27ae60"
        elif roi >= 15 and profit_margin >= 10:
            return "جيد جداً", "#2ecc71"
        elif roi >= 10 and profit_margin >= 5:
            return "جيد", "#f39c12"
        elif roi >= 5 and profit_margin >= 0:
            return "مقبول", "#f39c12"
        else:
            return "ضعيف", "#e74c3c"

    def generate_recommendations(self, roi, profit_margin, net_profit, total_investment):
        """إنشاء التوصيات بناءً على التحليل"""
        recommendations = []

        if net_profit <= 0:
            recommendations.append("• المشروع غير مربح حالياً - يحتاج إعادة تقييم شاملة")
            recommendations.append("• مراجعة هيكل التكاليف وتقليل النفقات غير الضرورية")
            recommendations.append("• البحث عن طرق لزيادة الإيرادات أو تحسين الأسعار")
        elif roi < 10:
            recommendations.append("• معدل العائد منخفض - يحتاج تحسين")
            recommendations.append("• مراجعة استراتيجية التسعير والتسويق")
            recommendations.append("• تحسين كفاءة العمليات لتقليل التكاليف")
        elif roi >= 20:
            recommendations.append("• معدل عائد ممتاز - المشروع مربح جداً")
            recommendations.append("• يمكن التوسع أو زيادة الاستثمار")
            recommendations.append("• الحفاظ على مستوى الجودة والخدمة")
        else:
            recommendations.append("• معدل عائد جيد ومقبول")
            recommendations.append("• يمكن تحسين الأداء بتطوير العمليات")
            recommendations.append("• مراقبة السوق والمنافسين باستمرار")

        if profit_margin < 5:
            recommendations.append("• هامش الربح منخفض - مراجعة هيكل التكاليف")
        elif profit_margin >= 15:
            recommendations.append("• هامش ربح ممتاز - استمرار الاستراتيجية الحالية")

        # إضافة التوصيات العامة
        recommendations.append("• إعداد خطة طوارئ للمخاطر المحتملة")
        recommendations.append("• مراجعة دورية للأداء المالي (شهرياً/ربع سنوي)")
        recommendations.append("• الاستثمار في التدريب وتطوير الفريق")

        # تحديث منطقة التوصيات
        self.recommendations_text.delete("1.0", tk.END)
        self.recommendations_text.insert("1.0", "\n".join(recommendations))

    def generate_report(self):
        """إنشاء تقرير شامل"""
        report = "تقرير دراسة الجدوى الشامل\n"
        report += "=" * 50 + "\n\n"

        # معلومات المشروع
        if hasattr(self.project_data, 'project_info'):
            project_name = self.project_data.project_info.get("اسم المشروع", "غير محدد")
            report += f"اسم المشروع: {project_name}\n"
            import datetime
            report += f"تاريخ التقرير: {datetime.datetime.now().strftime('%Y-%m-%d')}\n\n"

        # الملخص المالي
        report += "الملخص المالي:\n"
        report += "-" * 20 + "\n"

        revenue = self.entries["إجمالي الإيرادات"].get()
        costs = self.entries["إجمالي التكاليف السنوية"].get()
        depreciation = self.entries["قيمة إهلاك الأصول"].get()
        net_profit = self.entries["صافي الأرباح"].get()

        report += f"إجمالي الإيرادات السنوية: {revenue} ريال\n"
        report += f"إجمالي التكاليف السنوية: {costs} ريال\n"
        report += f"قيمة إهلاك الأصول: {depreciation} ريال\n"
        report += f"صافي الأرباح السنوية: {net_profit} ريال\n\n"

        # المؤشرات
        report += "المؤشرات المالية:\n"
        report += "-" * 20 + "\n"
        report += f"معدل العائد على الاستثمار: {self.roi_label.cget('text')}\n"
        report += f"فترة استرداد رأس المال: {self.payback_label.cget('text')}\n"
        report += f"هامش الربح الصافي: {self.margin_label.cget('text')}\n"
        report += f"التقييم العام: {self.evaluation_label.cget('text')}\n\n"

        # التوصيات
        recommendations = self.recommendations_text.get("1.0", tk.END).strip()
        if recommendations:
            report += "التوصيات:\n"
            report += "-" * 20 + "\n"
            report += recommendations + "\n\n"

        # الملاحظات
        notes = self.entries["ملاحظات إضافية"].get("1.0", tk.END).strip()
        if notes:
            report += "ملاحظات إضافية:\n"
            report += "-" * 20 + "\n"
            report += notes + "\n"

        # إظهار التقرير في نافذة منفصلة
        self.show_report_window(report)

    def show_report_window(self, report):
        """إظهار نافذة التقرير"""
        report_window = tk.Toplevel(self)
        report_window.title("تقرير دراسة الجدوى الشامل")
        report_window.geometry("700x600")
        report_window.configure(bg="#ffffff")

        tk.Label(report_window, text="تقرير دراسة الجدوى الشامل",
                font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        text_frame = tk.Frame(report_window, bg="#ffffff")
        text_frame.pack(fill="both", expand=True, padx=20, pady=10)

        text_widget = tk.Text(text_frame, font=("Tajawal", 10), wrap="word",
                            relief="solid", bd=1)
        text_widget.pack(fill="both", expand=True)

        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")

        text_widget.insert("1.0", report)
        text_widget.config(state="disabled")

        btn_frame = tk.Frame(report_window, bg="#ffffff")
        btn_frame.pack(fill="x", pady=20)

        tk.Button(btn_frame, text="📋 نسخ التقرير", font=("Tajawal", 11), bg="#3498db", fg="white",
                 command=lambda: self.copy_to_clipboard(report)).pack(side="right", padx=20)

        tk.Button(btn_frame, text="💾 حفظ كملف", font=("Tajawal", 11), bg="#27ae60", fg="white",
                 command=lambda: self.save_report_to_file(report)).pack(side="right", padx=10)

        tk.Button(btn_frame, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=report_window.destroy).pack(side="right", padx=10)

    def copy_to_clipboard(self, content):
        """نسخ المحتوى إلى الحافظة"""
        self.clipboard_clear()
        self.clipboard_append(content)
        messagebox.showinfo("نجح", "تم نسخ التقرير إلى الحافظة")

    def save_report_to_file(self, report):
        """حفظ التقرير كملف نصي"""
        from tkinter import filedialog

        filename = filedialog.asksaveasfilename(
            defaultextension=".txt",
            filetypes=[("ملفات نصية", "*.txt"), ("جميع الملفات", "*.*")],
            title="حفظ التقرير"
        )

        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(report)
                messagebox.showinfo("نجح", f"تم حفظ التقرير في:\n{filename}")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ التقرير: {str(e)}")

    def save_data(self):
        """حفظ البيانات في نموذج البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = entry.get("1.0", tk.END).strip()
            else:
                value = entry.get().strip()
            self.project_data.summary[field] = value

    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        for field, entry in self.entries.items():
            value = self.project_data.summary.get(field, "")
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
                entry.insert("1.0", value)
            else:
                entry.delete(0, tk.END)
                entry.insert(0, value)

        # تحديث البيانات التلقائية
        self.update_auto_data()

    def clear_data(self):
        """مسح جميع البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
            else:
                entry.delete(0, tk.END)

        # إعادة تعيين التسميات
        self.auto_revenue_label.config(text="0.00 ريال")
        self.auto_costs_label.config(text="0.00 ريال")
        self.auto_depreciation_label.config(text="0.00 ريال")
        self.auto_profit_label.config(text="0.00 ريال")

        self.roi_label.config(text="--%")
        self.payback_label.config(text="-- سنة")
        self.margin_label.config(text="--%")
        self.evaluation_label.config(text="غير محدد", fg="#95a5a6")

        self.recommendations_text.delete("1.0", tk.END)

        # مسح البيانات من النموذج
        self.project_data.summary.clear()
