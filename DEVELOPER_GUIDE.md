# دليل المطور - نظام دراسة الجدوى الشامل

## 🎯 نظرة عامة للمطورين

هذا الدليل مخصص للمطورين الذين يرغبون في فهم بنية النظام أو المساهمة في تطويره.

## 🏗️ بنية المشروع

### الملفات الرئيسية
```
saif/
├── main.py              # نقطة الدخول الرئيسية
├── run.py               # ملف التشغيل مع فحص المتطلبات
├── config.py            # إعدادات النظام
├── utils.py             # وظائف مساعدة
├── test_system.py       # اختبارات النظام
└── setup.py             # إعداد التوزيع
```

### مجلد الواجهات (ui/)
```
ui/
├── __init__.py
├── personal_info.py     # واجهة المعلومات الشخصية
├── project_info.py      # واجهة بيانات المشروع
├── market_analysis.py   # واجهة دراسة السوق
├── swot.py             # واجهة تحليل SWOT
├── marketing_mix.py     # واجهة المزيج التسويقي
├── production.py        # واجهة مستلزمات الإنتاج
├── financials.py        # واجهة الدراسة المالية
└── summary.py           # واجهة ملخص الأرباح
```

### مجلد البيانات (data/)
```
data/
├── __init__.py
├── models.py            # نماذج البيانات
├── data_manager.py      # إدارة البيانات
└── export_manager.py    # إدارة التصدير
```

## 🔧 المكونات الأساسية

### 1. نماذج البيانات (models.py)

#### ProjectData
الفئة الرئيسية لتخزين بيانات المشروع:

```python
class ProjectData:
    def __init__(self):
        self.personal_info = {}      # المعلومات الشخصية
        self.project_info = {}       # بيانات المشروع
        self.market_analysis = {}    # دراسة السوق
        self.swot = {}              # تحليل SWOT
        self.marketing_mix = {}      # المزيج التسويقي
        self.production = {}         # مستلزمات الإنتاج
        self.financials = {}         # الدراسة المالية
        self.summary = {}            # ملخص الأرباح
        self.metadata = {}           # معلومات إضافية
```

#### الفئات المساعدة
- `EquipmentItem`: عنصر معدة
- `RawMaterial`: مادة خام
- `FinancialItem`: عنصر مالي

### 2. مدير البيانات (data_manager.py)

#### DataManager
يدير عمليات الحفظ والتحميل:

```python
class DataManager:
    def save_project(self, project_data, filename=None)
    def load_project(self, filepath)
    def create_backup(self, filepath)
    def list_projects(self)
    def export_to_text(self, project_data, filepath)
```

### 3. مدير التصدير (export_manager.py)

#### ExportManager
يدير عمليات التصدير:

```python
class ExportManager:
    def export_to_pdf(self, project_data, filename=None)
    def export_to_word(self, project_data, filename=None)
    def export_to_excel(self, project_data, filename=None)
```

### 4. الإعدادات (config.py)

يحتوي على جميع إعدادات النظام:
- `UI_CONFIG`: إعدادات الواجهة
- `FONTS`: إعدادات الخطوط
- `EXPORT_CONFIG`: إعدادات التصدير
- `VALIDATION_CONFIG`: إعدادات التحقق
- `MESSAGES`: رسائل النظام

### 5. الوظائف المساعدة (utils.py)

وظائف مساعدة متنوعة:
- التحقق من صحة البيانات
- تنسيق البيانات
- معالجة الملفات
- الحسابات المالية

## 🎨 نمط التصميم

### بنية الواجهات
كل واجهة تتبع النمط التالي:

```python
class SectionFrame(tk.Frame):
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # إنشاء العناصر
        pass
    
    def save_data(self):
        # حفظ البيانات في project_data
        pass
    
    def load_data(self):
        # تحميل البيانات من project_data
        pass
    
    def clear_data(self):
        # مسح البيانات
        pass
```

### نمط الألوان
```python
COLORS = {
    "primary": "#4a90e2",      # أزرق رئيسي
    "secondary": "#1e3a8a",    # أزرق داكن
    "success": "#27ae60",      # أخضر
    "warning": "#f39c12",      # برتقالي
    "error": "#e74c3c",        # أحمر
    "background": "#f2f4f8",   # خلفية فاتحة
    "card": "#ffffff",         # أبيض
    "text": "#2c3e50"          # نص داكن
}
```

## 🔄 دورة حياة البيانات

### 1. إنشاء مشروع جديد
```
MainApp.__init__() → ProjectData() → setup_ui() → create_frames()
```

### 2. إدخال البيانات
```
User Input → Frame.entries → Frame.save_data() → ProjectData.section
```

### 3. حفظ المشروع
```
save_project() → collect_data() → DataManager.save_project() → JSON file
```

### 4. تحميل المشروع
```
load_project() → DataManager.load_project() → ProjectData.from_dict() → Frame.load_data()
```

### 5. تصدير التقرير
```
export_*() → collect_data() → ExportManager.export_*() → Output file
```

## 🧪 الاختبارات

### تشغيل الاختبارات
```bash
python test_system.py
```

### إضافة اختبارات جديدة
```python
def test_new_feature():
    """اختبار ميزة جديدة"""
    try:
        # كود الاختبار
        assert condition
        print("✅ الاختبار نجح")
        return True
    except Exception as e:
        print(f"❌ الاختبار فشل: {e}")
        return False
```

## 🔧 إضافة ميزات جديدة

### 1. إضافة قسم جديد

#### إنشاء الواجهة
```python
# ui/new_section.py
class NewSectionFrame(tk.Frame):
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # تصميم الواجهة
        pass
    
    def save_data(self):
        # حفظ البيانات
        for field, entry in self.entries.items():
            value = entry.get()
            self.project_data.new_section[field] = value
    
    def load_data(self):
        # تحميل البيانات
        for field, entry in self.entries.items():
            value = self.project_data.new_section.get(field, "")
            entry.delete(0, tk.END)
            entry.insert(0, value)
    
    def clear_data(self):
        # مسح البيانات
        for field, entry in self.entries.items():
            entry.delete(0, tk.END)
        self.project_data.new_section.clear()
```

#### تحديث نموذج البيانات
```python
# data/models.py
class ProjectData:
    def __init__(self):
        # ... الأقسام الموجودة
        self.new_section = {}  # القسم الجديد
    
    def to_dict(self):
        return {
            # ... الأقسام الموجودة
            "new_section": self.new_section
        }
    
    def from_dict(self, data):
        # ... الأقسام الموجودة
        self.new_section = data.get("new_section", {})
```

#### تحديث الواجهة الرئيسية
```python
# main.py
from ui.new_section import NewSectionFrame

SECTIONS = [
    # ... الأقسام الموجودة
    ("القسم الجديد", NewSectionFrame)
]
```

### 2. إضافة نوع تصدير جديد

```python
# data/export_manager.py
class ExportManager:
    def export_to_new_format(self, project_data, filename=None):
        """تصدير إلى تنسيق جديد"""
        try:
            # كود التصدير
            return filepath
        except Exception as e:
            raise Exception(f"فشل في التصدير: {str(e)}")
```

### 3. إضافة حسابات مالية جديدة

```python
# utils.py
def calculate_new_metric(value1, value2):
    """حساب مؤشر جديد"""
    try:
        result = (value1 * value2) / 100
        return result
    except (ValueError, ZeroDivisionError):
        return 0.0
```

## 🐛 تصحيح الأخطاء

### تفعيل وضع التصحيح
```python
# config.py
DEBUG_CONFIG = {
    "enabled": True,
    "log_level": "DEBUG",
    "show_performance_metrics": True
}
```

### إضافة سجلات
```python
import utils

logger = utils.setup_logging("DEBUG", "debug.log")

def some_function():
    logger.info("بدء تنفيذ الوظيفة")
    try:
        # كود الوظيفة
        logger.debug("تم تنفيذ خطوة معينة")
    except Exception as e:
        logger.error(f"خطأ في الوظيفة: {e}")
```

## 📦 التوزيع

### إنشاء حزمة للتوزيع
```bash
python setup.py sdist bdist_wheel
```

### تثبيت من المصدر
```bash
pip install -e .
```

## 🔒 الأمان

### التحقق من البيانات
- جميع المدخلات يتم التحقق منها
- تنظيف النصوص من الأحرف الضارة
- التحقق من أحجام الملفات

### حماية الملفات
- نسخ احتياطية تلقائية
- التحقق من صلاحيات الكتابة
- تشفير البيانات (للمستقبل)

## 🚀 الأداء

### تحسينات الأداء
- تحميل كسول للبيانات
- ذاكرة تخزين مؤقت للعمليات
- ضغط النسخ الاحتياطية

### مراقبة الأداء
```python
import time

def performance_monitor(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        print(f"الوظيفة {func.__name__} استغرقت {end_time - start_time:.2f} ثانية")
        return result
    return wrapper
```

## 📚 الموارد الإضافية

### المكتبات المستخدمة
- **tkinter**: واجهة المستخدم الرئيسية
- **reportlab**: تصدير PDF
- **python-docx**: تصدير Word
- **pandas**: تحليل البيانات
- **openpyxl**: تصدير Excel

### مراجع مفيدة
- [Tkinter Documentation](https://docs.python.org/3/library/tkinter.html)
- [ReportLab User Guide](https://www.reportlab.com/docs/reportlab-userguide.pdf)
- [Python-docx Documentation](https://python-docx.readthedocs.io/)

## 🤝 المساهمة

### خطوات المساهمة
1. Fork المشروع
2. إنشاء branch جديد
3. إضافة الميزة أو إصلاح الخطأ
4. كتابة الاختبارات
5. تحديث التوثيق
6. إرسال Pull Request

### معايير الكود
- استخدام أسماء متغيرات واضحة
- إضافة تعليقات باللغة العربية
- اتباع PEP 8 للتنسيق
- كتابة اختبارات للميزات الجديدة

---

**آخر تحديث**: ديسمبر 2024  
**الإصدار**: 1.0  
**المطورون**: فريق نظام دراسة الجدوى
