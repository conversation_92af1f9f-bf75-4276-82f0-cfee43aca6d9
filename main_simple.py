#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام دراسة الجدوى الشامل - إصدار مبسط يعمل
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sys

# إضافة مسار المشروع إلى sys.path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات المحلية
from data.models import ProjectData
from data.data_manager import DataManager
from data.export_manager import ExportManager
import modern_styles
from ui.personal_info import PersonalInfoFrame
from ui.project_info import ProjectInfoFrame
from ui.market_analysis import MarketAnalysisFrame
from ui.swot import SWOTFrame
from ui.marketing_mix import MarketingMixFrame
from ui.production import ProductionFrame
from ui.financials import FinancialsFrame
from ui.summary import SummaryFrame

# قائمة الأقسام
SECTIONS = [
    ("المعلومات الشخصية", PersonalInfoFrame),
    ("بيانات المشروع", ProjectInfoFrame),
    ("دراسة السوق والمنافسين", MarketAnalysisFrame),
    ("تحليل SWOT", SWOTFrame),
    ("المزيج التسويقي", MarketingMixFrame),
    ("مستلزمات الإنتاج", ProductionFrame),
    ("الدراسة المالية", FinancialsFrame),
    ("ملخص الربح السنوي", SummaryFrame)
]

class MainApp(tk.Tk):
    def __init__(self):
        super().__init__()
        self.title("نظام دراسة الجدوى الشامل")
        self.geometry("1400x900")
        self.configure(bg="#f8fafc")
        self.state('zoomed')  # تكبير النافذة
        
        # بيانات المشروع ومديري البيانات
        self.project_data = ProjectData()
        self.data_manager = DataManager()
        self.export_manager = ExportManager()
        
        # متغيرات التحكم
        self.current_section = None
        self.section_frames = {}
        self.section_buttons = {}
        
        # إعداد الواجهة
        self.setup_ui()
        
        # تحميل آخر مشروع محفوظ إن وجد
        self.load_last_project()

        # بدء الحفظ التلقائي
        self.auto_save()

    def setup_ui(self):
        """إعداد واجهة المستخدم المبسطة"""
        # شريط القوائم العلوي
        self.create_menu_bar()
        
        # العنوان الرئيسي الحديث
        self.create_modern_header()
        
        # إطار رئيسي مقسم مع تصميم حديث
        main_frame = tk.Frame(self, bg="#f8fafc")
        main_frame.pack(fill="both", expand=True, padx=15, pady=10)

        # قائمة أقسام حديثة
        self.create_modern_sidebar(main_frame)

        # منطقة المحتوى مع تأثيرات حديثة
        self.content_frame = tk.Frame(main_frame, bg="#ffffff", relief="flat", bd=0)
        self.content_frame.pack(side="left", fill="both", expand=True, padx=(20, 0))

        # تحميل الأقسام
        self.load_sections()

    def create_modern_header(self):
        """إنشاء عنوان حديث"""
        header_frame = tk.Frame(self, bg="#f8fafc")
        header_frame.pack(fill="x", pady=20)
        
        # العنوان مع أيقونة
        title_container = tk.Frame(header_frame, bg="#f8fafc")
        title_container.pack()
        
        # أيقونة
        icon_label = tk.Label(title_container, text="✨", font=("Arial", 24), 
                             bg="#f8fafc", fg="#f59e0b")
        icon_label.pack(side="right", padx=10)
        
        # العنوان مع خط عربي محسن
        title = tk.Label(title_container, text="نظام دراسة الجدوى الشامل",
                        font=modern_styles.get_safe_font("title"),
                        bg="#f8fafc", fg=modern_styles.get_color("primary"))
        title.pack(side="right")

        # العنوان الفرعي مع خط عربي محسن
        subtitle = tk.Label(header_frame, text="🚀 برنامج متطور لإعداد دراسات الجدوى الاقتصادية",
                           font=modern_styles.get_safe_font("subheading"),
                           bg="#f8fafc", fg=modern_styles.get_color("text_secondary"))
        subtitle.pack(pady=5)

    def create_modern_sidebar(self, parent):
        """إنشاء القائمة الجانبية الحديثة"""
        sidebar = tk.Frame(parent, bg="#ffffff", width=300, relief="flat", bd=1)
        sidebar.pack(side="right", fill="y", padx=(0, 20))
        sidebar.pack_propagate(False)
        
        # عنوان القائمة الجانبية
        header_frame = tk.Frame(sidebar, bg="#3b82f6")
        header_frame.pack(fill="x")
        
        title_label = tk.Label(header_frame, text="🗂️ أقسام دراسة الجدوى",
                              font=modern_styles.get_safe_font("heading"),
                              bg=modern_styles.get_color("primary"),
                              fg=modern_styles.get_color("text_white"), pady=15)
        title_label.pack()
        
        # حاوية الأقسام
        sections_frame = tk.Frame(sidebar, bg="#ffffff")
        sections_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # أزرار الأقسام الحديثة
        section_colors = [
            "#3b82f6", "#10b981", "#f59e0b", "#ef4444",
            "#8b5cf6", "#06b6d4", "#84cc16", "#f97316"
        ]
        
        for idx, (section, _) in enumerate(SECTIONS):
            color = section_colors[idx % len(section_colors)]
            btn_frame = self.create_section_button(sections_frame, section, idx + 1, color)
            self.section_buttons[section] = btn_frame

    def create_section_button(self, parent, section, number, color):
        """إنشاء زر قسم حديث"""
        # إطار الزر الرئيسي
        btn_frame = tk.Frame(parent, bg="#f8fafc", relief="flat", bd=1, cursor="hand2")
        btn_frame.pack(fill="x", pady=5)
        
        # إطار المحتوى
        content_frame = tk.Frame(btn_frame, bg="#f8fafc")
        content_frame.pack(fill="both", expand=True, padx=10, pady=8)
        
        # رقم القسم مع خلفية ملونة
        number_frame = tk.Frame(content_frame, bg=color, width=30, height=30)
        number_frame.pack(side="right", padx=(0, 10))
        number_frame.pack_propagate(False)
        
        number_label = tk.Label(number_frame, text=str(number),
                               font=modern_styles.get_safe_font("body_bold"),
                               bg=color, fg=modern_styles.get_color("text_white"))
        number_label.pack(expand=True)

        # نص القسم مع خط عربي محسن
        section_label = tk.Label(content_frame, text=section,
                                font=modern_styles.get_safe_font("body_bold"),
                                bg="#f8fafc", fg=modern_styles.get_color("text_primary"),
                                anchor="e", justify="right")
        section_label.pack(side="right", fill="both", expand=True)
        
        # ربط الأحداث
        def on_click(event=None):
            self.show_section(section)
        
        def on_enter(event):
            btn_frame.configure(bg="#e0e7ff")
            content_frame.configure(bg="#e0e7ff")
            section_label.configure(bg="#e0e7ff")
        
        def on_leave(event):
            if self.current_section != section:
                btn_frame.configure(bg="#f8fafc")
                content_frame.configure(bg="#f8fafc")
                section_label.configure(bg="#f8fafc")
        
        # ربط الأحداث بجميع العناصر
        for widget in [btn_frame, content_frame, section_label, number_label]:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)
        
        return btn_frame

    def load_sections(self):
        """تحميل جميع الأقسام"""
        for section, section_class in SECTIONS:
            frame = section_class(self.content_frame, self.project_data)
            frame.pack(fill="both", expand=True, padx=20, pady=20)
            frame.pack_forget()  # إخفاء جميع الإطارات في البداية
            self.section_frames[section] = frame
        
        # عرض القسم الأول افتراضياً
        self.show_section(SECTIONS[0][0])

    def show_section(self, section_name):
        """عرض قسم معين"""
        # إخفاء القسم الحالي
        if self.current_section and self.current_section in self.section_frames:
            self.section_frames[self.current_section].pack_forget()

            # إعادة تعيين لون الزر القديم
            if self.current_section in self.section_buttons:
                old_btn = self.section_buttons[self.current_section]
                old_btn.configure(bg="#f8fafc")
                for child in old_btn.winfo_children():
                    child.configure(bg="#f8fafc")
                    for grandchild in child.winfo_children():
                        if isinstance(grandchild, tk.Label):
                            grandchild.configure(bg="#f8fafc")

        # عرض القسم الجديد
        if section_name in self.section_frames:
            self.section_frames[section_name].pack(fill="both", expand=True, padx=20, pady=20)

        # تحديث لون الزر الحالي
        if section_name in self.section_buttons:
            current_btn = self.section_buttons[section_name]
            current_btn.configure(bg="#e0e7ff")
            for child in current_btn.winfo_children():
                child.configure(bg="#e0e7ff")
                for grandchild in child.winfo_children():
                    if isinstance(grandchild, tk.Label):
                        # تحديث لون جميع التسميات عدا الأرقام الملونة
                        if not grandchild.cget("text").isdigit():
                            grandchild.configure(bg="#e0e7ff")

        self.current_section = section_name

    def create_menu_bar(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self)
        self.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="مشروع جديد", command=self.new_project)
        file_menu.add_command(label="فتح مشروع", command=self.open_project)
        file_menu.add_command(label="حفظ المشروع", command=self.save_project)
        file_menu.add_separator()
        file_menu.add_command(label="تصدير إلى PDF", command=self.export_to_pdf)
        file_menu.add_command(label="خروج", command=self.quit)

    def new_project(self):
        """إنشاء مشروع جديد"""
        if messagebox.askyesno("مشروع جديد", "هل تريد إنشاء مشروع جديد؟"):
            self.project_data = ProjectData()
            for frame in self.section_frames.values():
                if hasattr(frame, 'clear_data'):
                    frame.clear_data()
            messagebox.showinfo("نجح", "تم إنشاء مشروع جديد")

    def save_project(self):
        """حفظ المشروع"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                if hasattr(frame, 'save_data'):
                    frame.save_data()
            
            # حفظ المشروع
            filepath = self.data_manager.save_project(self.project_data)
            messagebox.showinfo("نجح", f"تم حفظ المشروع في:\n{filepath}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المشروع:\n{str(e)}")

    def open_project(self):
        """فتح مشروع"""
        try:
            filepath = filedialog.askopenfilename(
                title="فتح مشروع",
                filetypes=[("ملفات JSON", "*.json"), ("جميع الملفات", "*.*")]
            )
            
            if filepath:
                self.project_data = self.data_manager.load_project(filepath)
                
                # تحديث جميع الأقسام
                for frame in self.section_frames.values():
                    if hasattr(frame, 'load_data'):
                        frame.load_data()
                
                messagebox.showinfo("نجح", "تم تحميل المشروع بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشروع:\n{str(e)}")

    def export_to_pdf(self):
        """تصدير إلى PDF"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                if hasattr(frame, 'save_data'):
                    frame.save_data()
            
            # تصدير إلى PDF
            filepath = self.export_manager.export_to_pdf(self.project_data)
            messagebox.showinfo("نجح", f"تم تصدير التقرير إلى:\n{filepath}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في التصدير:\n{str(e)}")

    def load_last_project(self):
        """تحميل آخر مشروع محفوظ"""
        try:
            projects = self.data_manager.list_projects()
            if projects:
                # تحميل أحدث مشروع
                latest_project = max(projects, key=lambda x: x['modified'])
                self.project_data = self.data_manager.load_project(latest_project['path'])
                
                # تحديث الأقسام بعد تحميلها
                self.after(100, self.update_sections_data)
        except:
            pass  # تجاهل الأخطاء في التحميل التلقائي

    def update_sections_data(self):
        """تحديث بيانات الأقسام"""
        for frame in self.section_frames.values():
            if hasattr(frame, 'load_data'):
                frame.load_data()

    def auto_save(self):
        """الحفظ التلقائي"""
        try:
            # جمع البيانات من جميع الأقسام
            for frame in self.section_frames.values():
                if hasattr(frame, 'save_data'):
                    frame.save_data()
            
            # حفظ تلقائي
            self.data_manager.save_project(self.project_data, "auto_save.json")
        except:
            pass  # تجاهل أخطاء الحفظ التلقائي
        
        # جدولة الحفظ التلقائي التالي (كل 5 دقائق)
        self.after(300000, self.auto_save)

    def on_closing(self):
        """عند إغلاق التطبيق"""
        try:
            # حفظ تلقائي قبل الإغلاق
            for frame in self.section_frames.values():
                if hasattr(frame, 'save_data'):
                    frame.save_data()
            self.data_manager.save_project(self.project_data, "last_session.json")
        except:
            pass
        self.destroy()

if __name__ == "__main__":
    app = MainApp()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()
