#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل نظام دراسة الجدوى الشامل - الإصدار الحديث
مع التحسينات والتصميم الجديد
"""

import sys
import os
import subprocess
import importlib.util
import time

def print_modern_header():
    """طباعة عنوان حديث للتطبيق"""
    print("=" * 70)
    print("✨ نظام دراسة الجدوى الشامل - الإصدار الحديث ✨")
    print("=" * 70)
    print("🎨 تصميم حضاري حديث | 🚀 واجهة Hero محسنة | 📊 لوحة معلومات متطورة")
    print("✨ مؤشر تقدم ثلاثي الأبعاد | 📈 رسوم بيانية محسنة | 🎯 حاويات أقسام مطورة")
    print("=" * 70)

def check_python_version():
    """التحقق من إصدار Python"""
    print("\n🔍 فحص متطلبات النظام...")
    
    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_required_packages():
    """التحقق من المكتبات المطلوبة"""
    print("\n📦 فحص المكتبات المطلوبة...")
    
    required_packages = {
        'tkinter': 'مدمج مع Python',
        'reportlab': 'pip install reportlab',
        'docx': 'pip install python-docx', 
        'pandas': 'pip install pandas',
        'openpyxl': 'pip install openpyxl'
    }
    
    missing_packages = []
    
    for package, install_cmd in required_packages.items():
        try:
            if package == 'tkinter':
                import tkinter
                print(f"✅ {package} - مدمج")
            elif package == 'docx':
                import docx
                print(f"✅ {package} - مثبت")
            else:
                importlib.import_module(package)
                print(f"✅ {package} - مثبت")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append((package, install_cmd))
    
    return missing_packages

def install_missing_packages(missing_packages):
    """تثبيت المكتبات المفقودة"""
    if not missing_packages:
        return True
    
    print("\n🔧 المكتبات المفقودة:")
    for package, install_cmd in missing_packages:
        print(f"   📦 {package}: {install_cmd}")
    
    response = input("\n❓ هل تريد تثبيت المكتبات المفقودة تلقائياً؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        print("\n🚀 جاري تثبيت المكتبات...")
        
        try:
            # تحديث pip أولاً
            print("📈 تحديث pip...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            # تثبيت المكتبات
            packages_to_install = []
            for package, install_cmd in missing_packages:
                if 'pip install' in install_cmd:
                    pkg_name = install_cmd.split('pip install ')[1]
                    packages_to_install.append(pkg_name)
            
            if packages_to_install:
                print(f"📦 تثبيت: {', '.join(packages_to_install)}")
                subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages_to_install,
                                    stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            
            print("✅ تم تثبيت جميع المكتبات بنجاح!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت المكتبات: {e}")
            print("💡 يرجى تثبيتها يدوياً باستخدام الأوامر المذكورة أعلاه")
            return False
    else:
        print("⚠️ يرجى تثبيت المكتبات المفقودة يدوياً قبل تشغيل البرنامج")
        return False

def check_modern_features():
    """فحص الميزات الحديثة"""
    print("\n🎨 فحص الميزات الحديثة...")
    
    features = [
        ("modern_styles.py", "أنماط التصميم الحديث"),
        ("main.py", "الواجهة الرئيسية المحدثة"),
        ("ui/personal_info.py", "واجهات الأقسام المحسنة"),
        ("config.py", "نظام الإعدادات المتقدم"),
        ("utils.py", "الوظائف المساعدة")
    ]
    
    all_features_available = True
    
    for file_path, description in features:
        if os.path.exists(file_path):
            print(f"✅ {description}")
        else:
            print(f"❌ {description} - مفقود")
            all_features_available = False
    
    if all_features_available:
        print("🎉 جميع الميزات الحديثة متوفرة!")
    else:
        print("⚠️ بعض الميزات الحديثة مفقودة")
    
    return all_features_available

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("\n📁 إنشاء المجلدات المطلوبة...")
    
    directories = [
        'data/projects', 
        'data/backups', 
        'data/templates', 
        'exports',
        'assets/fonts',
        'assets/icons',
        'assets/themes'
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"✅ {directory}")
    
    print("📂 تم إنشاء جميع المجلدات بنجاح")

def show_loading_animation():
    """عرض رسوم متحركة للتحميل"""
    print("\n🚀 جاري تحضير النظام الحديث...")
    
    loading_chars = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
    
    for i in range(20):
        char = loading_chars[i % len(loading_chars)]
        print(f"\r{char} تحميل الميزات الحديثة... {(i+1)*5}%", end="", flush=True)
        time.sleep(0.1)
    
    print("\r✅ تم تحضير النظام بنجاح!     ")

def run_modern_application():
    """تشغيل التطبيق الحديث"""
    print("\n🎨 تشغيل نظام دراسة الجدوى الحديث...")
    
    try:
        # تشغيل التطبيق
        import main
        print("🎉 تم تشغيل التطبيق بنجاح!")
        print("💡 استمتع بالتصميم الحديث والميزات المتطورة!")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        print("🔧 يرجى التحقق من وجود جميع الملفات المطلوبة")
        return False
    
    return True

def show_features_summary():
    """عرض ملخص الميزات الحديثة"""
    print("\n" + "=" * 70)
    print("🌟 الميزات الحديثة المتوفرة:")
    print("=" * 70)
    
    features = [
        "🎨 تصميم حضاري حديث مع خلفية متدرجة وتأثيرات بصرية",
        "✨ واجهة Hero ترحيبية مع أيقونات متحركة وشارات إنجاز",
        "📊 لوحة معلومات محسنة مع 8 بطاقات إحصائية ملونة",
        "🚀 مؤشر تقدم دائري ثلاثي الأبعاد مع رسائل تحفيزية",
        "📈 رسوم بيانية متطورة مع تدرجات وتأثيرات tooltip",
        "🎯 حاويات أقسام مطورة مع تأثيرات hover وتكبير",
        "📱 تصميم متجاوب يتكيف مع جميع أحجام الشاشات",
        "🎭 نظام ألوان عصري مع تدرجات جذابة",
        "⚡ تأثيرات انتقال ناعمة وتفاعلات متقدمة",
        "🛡️ أمان محسن مع نسخ احتياطية تلقائية"
    ]
    
    for feature in features:
        print(f"   {feature}")
    
    print("=" * 70)

def main():
    """الوظيفة الرئيسية"""
    print_modern_header()
    
    # فحص متطلبات النظام
    if not check_python_version():
        input("\n⏸️ اضغط Enter للخروج...")
        return
    
    # فحص المكتبات المطلوبة
    missing_packages = check_required_packages()
    
    # تثبيت المكتبات المفقودة إذا لزم الأمر
    if missing_packages:
        if not install_missing_packages(missing_packages):
            input("\n⏸️ اضغط Enter للخروج...")
            return
        
        # إعادة التحقق بعد التثبيت
        print("\n🔄 إعادة فحص المكتبات...")
        missing_packages = check_required_packages()
        
        if missing_packages:
            print("❌ لا تزال هناك مكتبات مفقودة")
            input("\n⏸️ اضغط Enter للخروج...")
            return
    
    # فحص الميزات الحديثة
    check_modern_features()
    
    # إنشاء المجلدات المطلوبة
    create_directories()
    
    # عرض رسوم متحركة للتحميل
    show_loading_animation()
    
    # عرض ملخص الميزات
    show_features_summary()
    
    # تشغيل التطبيق
    if run_modern_application():
        print("\n🎊 تم إغلاق التطبيق بنجاح")
        print("💝 شكراً لاستخدام نظام دراسة الجدوى الحديث!")
    else:
        input("\n⏸️ اضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
        print("👋 إلى اللقاء!")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("⏸️ اضغط Enter للخروج...")
