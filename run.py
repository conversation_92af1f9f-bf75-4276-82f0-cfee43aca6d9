#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف تشغيل مبسط لنظام دراسة الجدوى الشامل
يتحقق من المتطلبات ويشغل البرنامج
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 7):
        print("❌ خطأ: يتطلب Python 3.7 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def check_required_packages():
    """التحقق من المكتبات المطلوبة"""
    required_packages = {
        'tkinter': 'مدمج مع Python',
        'reportlab': 'pip install reportlab',
        'docx': 'pip install python-docx', 
        'pandas': 'pip install pandas',
        'openpyxl': 'pip install openpyxl'
    }
    
    missing_packages = []
    
    for package, install_cmd in required_packages.items():
        try:
            if package == 'tkinter':
                import tkinter
            elif package == 'docx':
                import docx
            else:
                importlib.import_module(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - غير مثبت")
            missing_packages.append((package, install_cmd))
    
    return missing_packages

def install_missing_packages(missing_packages):
    """تثبيت المكتبات المفقودة"""
    if not missing_packages:
        return True
    
    print("\n🔧 المكتبات المفقودة:")
    for package, install_cmd in missing_packages:
        print(f"   {package}: {install_cmd}")
    
    response = input("\nهل تريد تثبيت المكتبات المفقودة تلقائياً؟ (y/n): ")
    
    if response.lower() in ['y', 'yes', 'نعم']:
        print("\n📦 جاري تثبيت المكتبات...")
        
        try:
            # تحديث pip أولاً
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', '--upgrade', 'pip'])
            
            # تثبيت المكتبات
            packages_to_install = []
            for package, install_cmd in missing_packages:
                if 'pip install' in install_cmd:
                    pkg_name = install_cmd.split('pip install ')[1]
                    packages_to_install.append(pkg_name)
            
            if packages_to_install:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + packages_to_install)
            
            print("✅ تم تثبيت جميع المكتبات بنجاح!")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ فشل في تثبيت المكتبات: {e}")
            print("يرجى تثبيتها يدوياً باستخدام الأوامر المذكورة أعلاه")
            return False
    else:
        print("يرجى تثبيت المكتبات المفقودة يدوياً قبل تشغيل البرنامج")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    directories = ['data/projects', 'data/backups', 'data/templates', 'exports']
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
    
    print("✅ تم إنشاء المجلدات المطلوبة")

def run_application():
    """تشغيل التطبيق الرئيسي"""
    try:
        print("\n🚀 جاري تشغيل نظام دراسة الجدوى...")
        import main
        print("✅ تم تشغيل البرنامج بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        print("يرجى التحقق من وجود جميع الملفات المطلوبة")
        return False
    
    return True

def main():
    """الوظيفة الرئيسية"""
    print("=" * 50)
    print("🏢 نظام دراسة الجدوى الشامل")
    print("=" * 50)
    print("🔍 جاري التحقق من المتطلبات...\n")
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("اضغط Enter للخروج...")
        return
    
    # التحقق من المكتبات المطلوبة
    missing_packages = check_required_packages()
    
    # تثبيت المكتبات المفقودة إذا لزم الأمر
    if missing_packages:
        if not install_missing_packages(missing_packages):
            input("اضغط Enter للخروج...")
            return
        
        # إعادة التحقق بعد التثبيت
        print("\n🔍 إعادة التحقق من المكتبات...")
        missing_packages = check_required_packages()
        
        if missing_packages:
            print("❌ لا تزال هناك مكتبات مفقودة")
            input("اضغط Enter للخروج...")
            return
    
    # إنشاء المجلدات المطلوبة
    create_directories()
    
    # تشغيل التطبيق
    if run_application():
        print("\n✅ تم إغلاق البرنامج بنجاح")
    else:
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️ تم إيقاف البرنامج بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
