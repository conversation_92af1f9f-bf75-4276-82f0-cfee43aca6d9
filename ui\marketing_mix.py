import tkinter as tk
from tkinter import ttk, messagebox

class MarketingMixFrame(tk.Frame):
    """واجهة المزيج التسويقي (4Ps + People)"""
    
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg="#ffffff")
        title_frame.pack(fill="x", pady=(0, 20))
        
        tk.Label(title_frame, text="المزيج التسويقي (4Ps + People)", 
                font=("Tajawal", 18, "bold"), bg="#ffffff", fg="#1e3a8a").pack()
        
        # خط فاصل
        separator = tk.Frame(title_frame, height=2, bg="#4a90e2")
        separator.pack(fill="x", pady=10)
        
        # شرح المزيج التسويقي
        info_frame = tk.Frame(self, bg="#e8f4fd", relief="solid", bd=1)
        info_frame.pack(fill="x", padx=40, pady=10)
        
        info_text = """
المزيج التسويقي هو مجموعة من الأدوات التسويقية التي تستخدمها الشركة لتحقيق أهدافها:
• المنتج (Product): ما تقدمه للعملاء  • السعر (Price): كيف تسعر منتجاتك
• المكان (Place): أين وكيف تبيع  • الترويج (Promotion): كيف تروج لمنتجاتك  • الأشخاص (People): فريق العمل والعملاء
        """
        
        tk.Label(info_frame, text=info_text.strip(), font=("Tajawal", 10), 
                bg="#e8f4fd", fg="#2c3e50", justify="right").pack(pady=10, padx=20)
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(self, bg="#ffffff")
        scrollbar = ttk.Scrollbar(self, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # المحتوى الرئيسي - مطابق لقسم بيانات المشروع
        main_form = tk.Frame(scrollable_frame, bg="#ffffff")
        main_form.pack(fill="both", expand=True, padx=40)
        
        # تعريف عناصر المزيج التسويقي
        marketing_elements = [
            {
                "name": "المنتج",
                "icon": "📦",
                "color": "#3498db",
                "questions": [
                    "ما هو المنتج أو الخدمة التي تقدمها؟",
                    "ما هي مميزات وفوائد المنتج؟",
                    "كيف يختلف منتجك عن المنافسين؟",
                    "ما هي دورة حياة المنتج المتوقعة؟"
                ]
            },
            {
                "name": "السعر",
                "icon": "💰",
                "color": "#27ae60",
                "questions": [
                    "ما هي استراتيجية التسعير المتبعة؟",
                    "كيف تقارن أسعارك مع المنافسين؟",
                    "هل ستقدم خصومات أو عروض خاصة؟",
                    "ما هي طرق الدفع المقبولة؟"
                ]
            },
            {
                "name": "المكان",
                "icon": "📍",
                "color": "#e74c3c",
                "questions": [
                    "أين ستبيع منتجاتك؟ (متجر، أونلاين، موزعين)",
                    "ما هي قنوات التوزيع المستخدمة؟",
                    "كيف ستصل المنتجات للعملاء؟",
                    "ما هي المناطق الجغرافية المستهدفة؟"
                ]
            },
            {
                "name": "الترويج",
                "icon": "📢",
                "color": "#f39c12",
                "questions": [
                    "ما هي وسائل الإعلان والترويج؟",
                    "ما هي الرسالة التسويقية الأساسية؟",
                    "كيف ستصل للعملاء المستهدفين؟",
                    "ما هي ميزانية التسويق والإعلان؟"
                ]
            },
            {
                "name": "الأشخاص",
                "icon": "👥",
                "color": "#9b59b6",
                "questions": [
                    "من هو فريق العمل المطلوب؟",
                    "ما هي مهارات وخبرات الفريق؟",
                    "كيف ستدرب الموظفين على خدمة العملاء؟",
                    "من هم العملاء المستهدفون؟"
                ]
            }
        ]
        
        # إنشاء قسم لكل عنصر
        for element in marketing_elements:
            self.create_element_section(main_form, element)
        
        # قسم الخطة التسويقية الشاملة
        comprehensive_section = tk.LabelFrame(main_form, text="الخطة التسويقية الشاملة", 
                                            font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                            fg="#2c3e50", relief="solid", bd=1)
        comprehensive_section.pack(fill="x", pady=20)
        
        # الأهداف التسويقية
        goals_frame = tk.Frame(comprehensive_section, bg="#ffffff")
        goals_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(goals_frame, text="الأهداف التسويقية:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        goals_text = tk.Text(goals_frame, font=("Tajawal", 11), height=3, 
                           relief="solid", bd=1, wrap="word")
        goals_text.pack(fill="x", pady=5)
        self.entries["الأهداف التسويقية"] = goals_text
        
        # الميزانية التسويقية
        budget_frame = tk.Frame(comprehensive_section, bg="#ffffff")
        budget_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(budget_frame, text="الميزانية التسويقية الشهرية:", 
                font=("Tajawal", 12, "bold"), bg="#ffffff", fg="#2c3e50", 
                width=25, anchor="e").pack(side="right", padx=(0, 10))
        
        budget_entry = tk.Entry(budget_frame, font=("Tajawal", 11), 
                              justify="right", relief="solid", bd=1)
        budget_entry.pack(side="right", fill="x", expand=True)
        self.entries["الميزانية التسويقية الشهرية"] = budget_entry
        
        # الجدول الزمني
        timeline_frame = tk.Frame(comprehensive_section, bg="#ffffff")
        timeline_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(timeline_frame, text="الجدول الزمني للتنفيذ:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        timeline_text = tk.Text(timeline_frame, font=("Tajawal", 11), height=4, 
                              relief="solid", bd=1, wrap="word")
        timeline_text.pack(fill="x", pady=5)
        self.entries["الجدول الزمني للتنفيذ"] = timeline_text
        
        # أزرار العمليات - مطابقة لقسم بيانات المشروع
        buttons_frame = tk.Frame(main_form, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)
        
        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat", 
                    "padx": 20, "pady": 8, "cursor": "hand2"}
        
        tk.Button(buttons_frame, text="💾 حفظ الخطة", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="📊 تحليل المزيج", bg="#3498db", fg="white",
                 command=self.analyze_mix, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="📋 إنشاء ملخص", bg="#9b59b6", fg="white",
                 command=self.create_summary, **btn_style).pack(side="right", padx=10)
    
    def create_element_section(self, parent, element):
        """إنشاء قسم لعنصر من عناصر المزيج التسويقي"""
        # إطار القسم
        section_frame = tk.LabelFrame(parent, text=f"{element['icon']} {element['name']}", 
                                    font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                    fg=element["color"], relief="solid", bd=1)
        section_frame.pack(fill="x", pady=10)
        
        # الأسئلة الإرشادية
        questions_frame = tk.Frame(section_frame, bg="#f8f9fa", relief="solid", bd=1)
        questions_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(questions_frame, text="أسئلة إرشادية:", font=("Tajawal", 10, "bold"), 
                bg="#f8f9fa", fg="#2c3e50").pack(anchor="e", padx=5, pady=2)
        
        for question in element["questions"]:
            tk.Label(questions_frame, text=f"• {question}", font=("Tajawal", 9), 
                    bg="#f8f9fa", fg="#5a6c7d", anchor="e", justify="right").pack(anchor="e", padx=10)
        
        # منطقة الإجابة
        answer_frame = tk.Frame(section_frame, bg="#ffffff")
        answer_frame.pack(fill="x", padx=10, pady=10)
        
        tk.Label(answer_frame, text=f"خطة {element['name']}:", font=("Tajawal", 11, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        text_widget = tk.Text(answer_frame, font=("Tajawal", 11), height=5, 
                            relief="solid", bd=1, wrap="word")
        text_widget.pack(fill="x", pady=5)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(answer_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        
        self.entries[element["name"]] = text_widget
    
    def analyze_mix(self):
        """تحليل المزيج التسويقي وإظهار التقييم"""
        analysis = "تحليل المزيج التسويقي:\n\n"
        
        elements = ["المنتج", "السعر", "المكان", "الترويج", "الأشخاص"]
        completed_elements = []
        missing_elements = []
        
        for element in elements:
            content = self.entries[element].get("1.0", tk.END).strip()
            if content:
                completed_elements.append(element)
                analysis += f"✅ {element}: مكتمل\n"
            else:
                missing_elements.append(element)
                analysis += f"❌ {element}: غير مكتمل\n"
        
        analysis += f"\n📊 التقييم العام:\n"
        completion_rate = (len(completed_elements) / len(elements)) * 100
        analysis += f"• نسبة الاكتمال: {completion_rate:.0f}%\n"
        
        if completion_rate >= 80:
            analysis += "• التقييم: ممتاز - المزيج التسويقي شامل\n"
        elif completion_rate >= 60:
            analysis += "• التقييم: جيد - يحتاج بعض التحسينات\n"
        elif completion_rate >= 40:
            analysis += "• التقييم: متوسط - يحتاج تطوير كبير\n"
        else:
            analysis += "• التقييم: ضعيف - يحتاج إعادة تخطيط\n"
        
        if missing_elements:
            analysis += f"\n⚠️ العناصر المفقودة: {', '.join(missing_elements)}\n"
        
        # إظهار النتيجة في نافذة منفصلة
        self.show_analysis_window("تحليل المزيج التسويقي", analysis)
    
    def create_summary(self):
        """إنشاء ملخص شامل للمزيج التسويقي"""
        summary = "ملخص المزيج التسويقي:\n\n"
        
        elements = ["المنتج", "السعر", "المكان", "الترويج", "الأشخاص"]
        
        for element in elements:
            content = self.entries[element].get("1.0", tk.END).strip()
            if content:
                summary += f"🔹 {element}:\n{content}\n\n"
        
        # إضافة المعلومات الإضافية
        goals = self.entries["الأهداف التسويقية"].get("1.0", tk.END).strip()
        if goals:
            summary += f"🎯 الأهداف التسويقية:\n{goals}\n\n"
        
        budget = self.entries["الميزانية التسويقية الشهرية"].get().strip()
        if budget:
            summary += f"💰 الميزانية الشهرية: {budget}\n\n"
        
        timeline = self.entries["الجدول الزمني للتنفيذ"].get("1.0", tk.END).strip()
        if timeline:
            summary += f"📅 الجدول الزمني:\n{timeline}\n"
        
        # إظهار الملخص في نافذة منفصلة
        self.show_analysis_window("ملخص المزيج التسويقي", summary)
    
    def show_analysis_window(self, title, content):
        """إظهار نافذة التحليل أو الملخص"""
        analysis_window = tk.Toplevel(self)
        analysis_window.title(title)
        analysis_window.geometry("600x500")
        analysis_window.configure(bg="#ffffff")
        
        # العنوان
        tk.Label(analysis_window, text=title, font=("Tajawal", 16, "bold"), 
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)
        
        # منطقة النص
        text_frame = tk.Frame(analysis_window, bg="#ffffff")
        text_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        text_widget = tk.Text(text_frame, font=("Tajawal", 11), wrap="word", 
                            relief="solid", bd=1)
        text_widget.pack(fill="both", expand=True)
        
        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")
        
        text_widget.insert("1.0", content)
        text_widget.config(state="disabled")
        
        # أزرار
        btn_frame = tk.Frame(analysis_window, bg="#ffffff")
        btn_frame.pack(fill="x", pady=20)
        
        tk.Button(btn_frame, text="📋 نسخ", font=("Tajawal", 11), bg="#3498db", fg="white",
                 command=lambda: self.copy_to_clipboard(content)).pack(side="right", padx=20)
        
        tk.Button(btn_frame, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=analysis_window.destroy).pack(side="right", padx=10)
    
    def copy_to_clipboard(self, content):
        """نسخ المحتوى إلى الحافظة"""
        self.clipboard_clear()
        self.clipboard_append(content)
        messagebox.showinfo("نجح", "تم نسخ المحتوى إلى الحافظة")
    
    def save_data(self):
        """حفظ البيانات في نموذج البيانات والانتقال للقسم التالي"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = entry.get("1.0", tk.END).strip()
            else:
                value = entry.get().strip()
            self.project_data.marketing_mix[field] = value

        # الانتقال إلى القسم التالي
        parent = self.master
        while parent and not hasattr(parent, 'go_to_next_section'):
            parent = parent.master
        if parent and hasattr(parent, 'go_to_next_section'):
            parent.go_to_next_section()
    
    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        for field, entry in self.entries.items():
            value = self.project_data.marketing_mix.get(field, "")
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
                entry.insert("1.0", value)
            else:
                entry.delete(0, tk.END)
                entry.insert(0, value)
    
    def clear_data(self):
        """مسح جميع البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
            else:
                entry.delete(0, tk.END)
        
        # مسح البيانات من النموذج
        self.project_data.marketing_mix.clear()
