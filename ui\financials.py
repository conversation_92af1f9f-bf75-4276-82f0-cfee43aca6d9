import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import modern_styles

class FinancialsFrame(tk.Frame):
    """واجهة الدراسة المالية"""
    
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.setup_ui()
    
    def setup_ui(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg="#ffffff")
        title_frame.pack(fill="x", pady=(0, 20))
        
        tk.Label(title_frame, text="الدراسة المالية", 
                font=("Tajawal", 18, "bold"), bg="#ffffff", fg="#1e3a8a").pack()
        
        # خط فاصل
        separator = tk.Frame(title_frame, height=2, bg="#4a90e2")
        separator.pack(fill="x", pady=10)
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(self, bg="#ffffff")
        scrollbar = ttk.Scrollbar(self, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # المحتوى الرئيسي - محاذاة مع القائمة الجانبية
        main_form = tk.Frame(scrollable_frame, bg="#ffffff")
        main_form.pack(fill="both", expand=True, padx=20, pady=20)  # مسافة جانبية قليلة للمحاذاة مع القائمة
        
        # قسم تكاليف التأسيس
        self.create_startup_costs_section(main_form)
        
        # قسم رأس المال الثابت
        self.create_fixed_capital_section(main_form)
        
        # قسم رأس المال العامل
        self.create_working_capital_section(main_form)
        
        # قسم تقدير الأرباح الشهرية
        self.create_monthly_profits_section(main_form)
        
        # قسم تقدير الإيرادات السنوية
        self.create_yearly_revenues_section(main_form)
        
        # قسم الملخص المالي
        self.create_financial_summary(main_form)
        
        # أزرار العمليات - محاذاة مع القائمة الجانبية
        buttons_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20, padx=20)  # نفس مسافة الحقول
        
        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat", 
                    "padx": 20, "pady": 8, "cursor": "hand2"}
        
        tk.Button(buttons_frame, text="💾 حفظ البيانات", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🧮 حساب الإجماليات", bg="#3498db", fg="white",
                 command=self.calculate_all_totals, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="📊 تحليل مالي", bg="#9b59b6", fg="white",
                 command=self.financial_analysis, **btn_style).pack(side="right", padx=10)
    
    def create_startup_costs_section(self, parent):
        """إنشاء قسم تكاليف التأسيس"""
        startup_section = tk.LabelFrame(parent, text="💼 تكاليف التأسيس (ما قبل التشغيل)", 
                                      font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                      fg="#2c3e50", relief="solid", bd=1)
        startup_section.pack(fill="x", pady=10)
        
        # أزرار الإدارة
        startup_buttons = tk.Frame(startup_section, bg="#ffffff")
        startup_buttons.pack(fill="x", padx=10, pady=5)
        
        btn_style = {"font": ("Tajawal", 10), "relief": "flat", "padx": 15, "pady": 5}
        
        tk.Button(startup_buttons, text="➕ إضافة بند", bg="#27ae60", fg="white",
                 command=lambda: self.add_financial_item(self.startup_tree, "تكلفة تأسيس"), 
                 **btn_style).pack(side="right", padx=5)
        
        tk.Button(startup_buttons, text="✏️ تعديل", bg="#f39c12", fg="white",
                 command=lambda: self.edit_financial_item(self.startup_tree), 
                 **btn_style).pack(side="right", padx=5)
        
        tk.Button(startup_buttons, text="🗑️ حذف", bg="#e74c3c", fg="white",
                 command=lambda: self.delete_financial_item(self.startup_tree), 
                 **btn_style).pack(side="right", padx=5)
        
        # جدول تكاليف التأسيس
        startup_frame = tk.Frame(startup_section, bg="#ffffff")
        startup_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        columns = ("البند", "المبلغ")
        self.startup_tree = ttk.Treeview(startup_frame, columns=columns, show="headings", height=5)
        
        # تكوين الأعمدة مع محاذاة RTL صحيحة
        for col in columns:
            self.startup_tree.heading(col, text=col, anchor="e")  # محاذاة العناوين لليمين
            if col == "البند":
                self.startup_tree.column(col, width=300, anchor="e")  # محاذاة النص لليمين
            else:
                self.startup_tree.column(col, width=150, anchor="e")  # محاذاة الأرقام لليمين

        startup_scrollbar = ttk.Scrollbar(startup_frame, orient="vertical", command=self.startup_tree.yview)
        self.startup_tree.configure(yscrollcommand=startup_scrollbar.set)

        # ترتيب الجدول في الجهة الشرقية (اليمين)
        self.startup_tree.pack(side="left", fill="both", expand=True)
        startup_scrollbar.pack(side="right", fill="y")
        
        # إضافة بنود افتراضية
        default_items = ["رخصة المشروع", "توصيل ماء/كهرباء/هاتف", "رسوم خلو/نقل", "مصاريف أخرى"]
        for item in default_items:
            self.startup_tree.insert("", "end", values=(item, "0.00"))
        
        # إجمالي تكاليف التأسيس
        startup_total_frame = tk.Frame(startup_section, bg="#ffffff")
        startup_total_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(startup_total_frame, text="إجمالي تكاليف التأسيس:", 
                font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(side="right", padx=10)
        
        self.startup_total_label = tk.Label(startup_total_frame, text="0.00 د.ع",
                                          font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#e74c3c")
        self.startup_total_label.pack(side="right")
    
    def create_fixed_capital_section(self, parent):
        """إنشاء قسم رأس المال الثابت"""
        fixed_section = tk.LabelFrame(parent, text="🏗️ رأس المال الثابت", 
                                    font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                    fg="#2c3e50", relief="solid", bd=1)
        fixed_section.pack(fill="x", pady=10)
        
        # أزرار الإدارة
        fixed_buttons = tk.Frame(fixed_section, bg="#ffffff")
        fixed_buttons.pack(fill="x", padx=10, pady=5)
        
        btn_style = {"font": ("Tajawal", 10), "relief": "flat", "padx": 15, "pady": 5}
        
        tk.Button(fixed_buttons, text="➕ إضافة بند", bg="#27ae60", fg="white",
                 command=lambda: self.add_financial_item(self.fixed_tree, "رأس مال ثابت"), 
                 **btn_style).pack(side="right", padx=5)
        
        tk.Button(fixed_buttons, text="✏️ تعديل", bg="#f39c12", fg="white",
                 command=lambda: self.edit_financial_item(self.fixed_tree), 
                 **btn_style).pack(side="right", padx=5)
        
        tk.Button(fixed_buttons, text="🗑️ حذف", bg="#e74c3c", fg="white",
                 command=lambda: self.delete_financial_item(self.fixed_tree), 
                 **btn_style).pack(side="right", padx=5)
        
        # جدول رأس المال الثابت
        fixed_frame = tk.Frame(fixed_section, bg="#ffffff")
        fixed_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        columns = ("البند", "المبلغ")
        self.fixed_tree = ttk.Treeview(fixed_frame, columns=columns, show="headings", height=5)
        
        # تكوين الأعمدة مع محاذاة RTL صحيحة
        for col in columns:
            self.fixed_tree.heading(col, text=col, anchor="e")  # محاذاة العناوين لليمين
            if col == "البند":
                self.fixed_tree.column(col, width=300, anchor="e")  # محاذاة النص لليمين
            else:
                self.fixed_tree.column(col, width=150, anchor="e")  # محاذاة الأرقام لليمين

        fixed_scrollbar = ttk.Scrollbar(fixed_frame, orient="vertical", command=self.fixed_tree.yview)
        self.fixed_tree.configure(yscrollcommand=fixed_scrollbar.set)

        # ترتيب الجدول في الجهة الشرقية (اليمين)
        self.fixed_tree.pack(side="left", fill="both", expand=True)
        fixed_scrollbar.pack(side="right", fill="y")
        
        # إضافة بنود افتراضية
        default_items = ["تجهيز المحل", "الأثاث", "الآلات والمعدات", "نفقات أخرى"]
        for item in default_items:
            self.fixed_tree.insert("", "end", values=(item, "0.00"))
        
        # إجمالي رأس المال الثابت
        fixed_total_frame = tk.Frame(fixed_section, bg="#ffffff")
        fixed_total_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(fixed_total_frame, text="إجمالي رأس المال الثابت:", 
                font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(side="right", padx=10)
        
        self.fixed_total_label = tk.Label(fixed_total_frame, text="0.00 د.ع",
                                        font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#27ae60")
        self.fixed_total_label.pack(side="right")
    
    def create_working_capital_section(self, parent):
        """إنشاء قسم رأس المال العامل"""
        working_section = tk.LabelFrame(parent, text="⚙️ رأس المال العامل (تشغيلي/شهري)", 
                                      font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                      fg="#2c3e50", relief="solid", bd=1)
        working_section.pack(fill="x", pady=10)
        
        # أزرار الإدارة
        working_buttons = tk.Frame(working_section, bg="#ffffff")
        working_buttons.pack(fill="x", padx=10, pady=5)
        
        btn_style = {"font": ("Tajawal", 10), "relief": "flat", "padx": 15, "pady": 5}
        
        tk.Button(working_buttons, text="➕ إضافة بند", bg="#27ae60", fg="white",
                 command=lambda: self.add_working_capital_item(), 
                 **btn_style).pack(side="right", padx=5)
        
        tk.Button(working_buttons, text="✏️ تعديل", bg="#f39c12", fg="white",
                 command=lambda: self.edit_working_capital_item(), 
                 **btn_style).pack(side="right", padx=5)
        
        tk.Button(working_buttons, text="🗑️ حذف", bg="#e74c3c", fg="white",
                 command=lambda: self.delete_financial_item(self.working_tree), 
                 **btn_style).pack(side="right", padx=5)
        
        # جدول رأس المال العامل
        working_frame = tk.Frame(working_section, bg="#ffffff")
        working_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        columns = ("البند", "ثابت/متغير", "المبلغ")
        self.working_tree = ttk.Treeview(working_frame, columns=columns, show="headings", height=7)
        
        # تكوين الأعمدة مع محاذاة RTL صحيحة
        for col in columns:
            self.working_tree.heading(col, text=col, anchor="e")  # محاذاة العناوين لليمين
            if col == "البند":
                self.working_tree.column(col, width=200, anchor="e")  # محاذاة النص لليمين
            elif col == "ثابت/متغير":
                self.working_tree.column(col, width=100, anchor="e")  # محاذاة النوع لليمين
            else:
                self.working_tree.column(col, width=120, anchor="e")  # محاذاة الأرقام لليمين

        working_scrollbar = ttk.Scrollbar(working_frame, orient="vertical", command=self.working_tree.yview)
        self.working_tree.configure(yscrollcommand=working_scrollbar.set)

        # ترتيب الجدول في الجهة الشرقية (اليمين)
        self.working_tree.pack(side="left", fill="both", expand=True)
        working_scrollbar.pack(side="right", fill="y")
        
        # إضافة بنود افتراضية
        default_items = [
            ("رواتب", "ثابت", "0.00"),
            ("إيجار", "ثابت", "0.00"),
            ("تسويق/دعاية", "ثابت", "0.00"),
            ("مواد خام", "متغير", "0.00"),
            ("أجور عمال", "متغير", "0.00"),
            ("فواتير/نقل", "متغير", "0.00"),
            ("أخرى", "", "0.00")
        ]
        for item in default_items:
            self.working_tree.insert("", "end", values=item)
        
        # إجمالي رأس المال العامل
        working_total_frame = tk.Frame(working_section, bg="#ffffff")
        working_total_frame.pack(fill="x", padx=10, pady=5)
        
        tk.Label(working_total_frame, text="إجمالي رأس المال العامل الشهري:", 
                font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(side="right", padx=10)
        
        self.working_total_label = tk.Label(working_total_frame, text="0.00 د.ع",
                                          font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#3498db")
        self.working_total_label.pack(side="right")

    def create_monthly_profits_section(self, parent):
        """إنشاء قسم تقدير الأرباح الشهرية"""
        profits_section = tk.LabelFrame(parent, text="📈 تقدير الأرباح والخسائر الشهرية",
                                      font=("Tajawal", 12, "bold"), bg="#ffffff",
                                      fg="#2c3e50", relief="solid", bd=1)
        profits_section.pack(fill="x", pady=10)

        # أزرار الإدارة
        profits_buttons = tk.Frame(profits_section, bg="#ffffff")
        profits_buttons.pack(fill="x", padx=10, pady=5)

        btn_style = {"font": ("Tajawal", 10), "relief": "flat", "padx": 15, "pady": 5}

        tk.Button(profits_buttons, text="➕ إضافة منتج", bg="#27ae60", fg="white",
                 command=self.add_product_profit, **btn_style).pack(side="right", padx=5)

        tk.Button(profits_buttons, text="✏️ تعديل", bg="#f39c12", fg="white",
                 command=self.edit_product_profit, **btn_style).pack(side="right", padx=5)

        tk.Button(profits_buttons, text="🗑️ حذف", bg="#e74c3c", fg="white",
                 command=lambda: self.delete_financial_item(self.profits_tree),
                 **btn_style).pack(side="right", padx=5)

        # جدول الأرباح الشهرية
        profits_frame = tk.Frame(profits_section, bg="#ffffff")
        profits_frame.pack(fill="both", expand=True, padx=10, pady=5)

        columns = ("المنتج/الخدمة", "وحدات البيع", "تكلفة الوحدة", "إجمالي التكلفة",
                  "سعر البيع", "إجمالي الإيراد", "إجمالي الربح")
        self.profits_tree = ttk.Treeview(profits_frame, columns=columns, show="headings", height=5)

        # تكوين الأعمدة مع محاذاة RTL صحيحة
        for col in columns:
            self.profits_tree.heading(col, text=col, anchor="e")  # محاذاة العناوين لليمين
            if col == "المنتج/الخدمة":
                self.profits_tree.column(col, width=150, anchor="e")  # محاذاة النص لليمين
            else:
                self.profits_tree.column(col, width=100, anchor="e")  # محاذاة الأرقام لليمين

        profits_scrollbar = ttk.Scrollbar(profits_frame, orient="vertical", command=self.profits_tree.yview)
        self.profits_tree.configure(yscrollcommand=profits_scrollbar.set)

        # ترتيب الجدول في الجهة الشرقية (اليمين)
        self.profits_tree.pack(side="left", fill="both", expand=True)
        profits_scrollbar.pack(side="right", fill="y")

        # إجمالي الأرباح الشهرية
        profits_total_frame = tk.Frame(profits_section, bg="#ffffff")
        profits_total_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(profits_total_frame, text="إجمالي الأرباح الشهرية:",
                font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(side="right", padx=10)

        self.profits_total_label = tk.Label(profits_total_frame, text="0.00 د.ع",
                                          font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#27ae60")
        self.profits_total_label.pack(side="right")

    def create_yearly_revenues_section(self, parent):
        """إنشاء قسم تقدير الإيرادات السنوية"""
        yearly_section = tk.LabelFrame(parent, text="📊 تقدير الإيرادات السنوية",
                                     font=("Tajawal", 12, "bold"), bg="#ffffff",
                                     fg="#2c3e50", relief="solid", bd=1)
        yearly_section.pack(fill="x", pady=10)

        # أزرار الإدارة
        yearly_buttons = tk.Frame(yearly_section, bg="#ffffff")
        yearly_buttons.pack(fill="x", padx=10, pady=5)

        btn_style = {"font": ("Tajawal", 10), "relief": "flat", "padx": 15, "pady": 5}

        tk.Button(yearly_buttons, text="➕ إضافة شهر", bg="#27ae60", fg="white",
                 command=self.add_monthly_revenue, **btn_style).pack(side="right", padx=5)

        tk.Button(yearly_buttons, text="✏️ تعديل", bg="#f39c12", fg="white",
                 command=self.edit_monthly_revenue, **btn_style).pack(side="right", padx=5)

        tk.Button(yearly_buttons, text="🗑️ حذف", bg="#e74c3c", fg="white",
                 command=lambda: self.delete_financial_item(self.yearly_tree),
                 **btn_style).pack(side="right", padx=5)

        tk.Button(yearly_buttons, text="📅 ملء الأشهر", bg="#9b59b6", fg="white",
                 command=self.fill_all_months, **btn_style).pack(side="right", padx=5)

        # جدول الإيرادات السنوية
        yearly_frame = tk.Frame(yearly_section, bg="#ffffff")
        yearly_frame.pack(fill="both", expand=True, padx=10, pady=5)

        columns = ("الشهر", "منتج 1", "منتج 2", "منتج 3", "منتج 4", "الإجمالي")
        self.yearly_tree = ttk.Treeview(yearly_frame, columns=columns, show="headings", height=6)

        # تكوين الأعمدة مع محاذاة RTL صحيحة
        for col in columns:
            self.yearly_tree.heading(col, text=col, anchor="e")  # محاذاة العناوين لليمين
            if col == "الشهر":
                self.yearly_tree.column(col, width=100, anchor="e")  # محاذاة الشهر لليمين
            else:
                self.yearly_tree.column(col, width=90, anchor="e")  # محاذاة الأرقام لليمين

        yearly_scrollbar = ttk.Scrollbar(yearly_frame, orient="vertical", command=self.yearly_tree.yview)
        self.yearly_tree.configure(yscrollcommand=yearly_scrollbar.set)

        # ترتيب الجدول في الجهة الشرقية (اليمين)
        self.yearly_tree.pack(side="left", fill="both", expand=True)
        yearly_scrollbar.pack(side="right", fill="y")

        # إجمالي الإيرادات السنوية
        yearly_total_frame = tk.Frame(yearly_section, bg="#ffffff")
        yearly_total_frame.pack(fill="x", padx=10, pady=5)

        tk.Label(yearly_total_frame, text="إجمالي الإيرادات السنوية:",
                font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#2c3e50").pack(side="right", padx=10)

        self.yearly_total_label = tk.Label(yearly_total_frame, text="0.00 د.ع",
                                         font=("Tajawal", 11, "bold"), bg="#ffffff", fg="#9b59b6")
        self.yearly_total_label.pack(side="right")

    def create_financial_summary(self, parent):
        """إنشاء قسم الملخص المالي"""
        summary_section = tk.LabelFrame(parent, text="💰 الملخص المالي الشامل",
                                      font=("Tajawal", 12, "bold"), bg="#ffffff",
                                      fg="#2c3e50", relief="solid", bd=1)
        summary_section.pack(fill="x", pady=10)

        # إطار الملخص
        summary_frame = tk.Frame(summary_section, bg="#f8f9fa", relief="solid", bd=1)
        summary_frame.pack(fill="x", padx=10, pady=10)

        # التكاليف الإجمالية
        costs_frame = tk.LabelFrame(summary_frame, text="التكاليف", font=("Tajawal", 11, "bold"),
                                  bg="#f8f9fa", fg="#e74c3c")
        costs_frame.pack(fill="x", padx=10, pady=5)

        # تكاليف التأسيس
        startup_summary_frame = tk.Frame(costs_frame, bg="#f8f9fa")
        startup_summary_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(startup_summary_frame, text="تكاليف التأسيس:",
                font=("Tajawal", 10), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=5)

        self.summary_startup_label = tk.Label(startup_summary_frame, text="0.00 د.ع",
                                            font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#e74c3c")
        self.summary_startup_label.pack(side="right")

        # رأس المال الثابت
        fixed_summary_frame = tk.Frame(costs_frame, bg="#f8f9fa")
        fixed_summary_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(fixed_summary_frame, text="رأس المال الثابت:",
                font=("Tajawal", 10), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=5)

        self.summary_fixed_label = tk.Label(fixed_summary_frame, text="0.00 د.ع",
                                          font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#e74c3c")
        self.summary_fixed_label.pack(side="right")

        # رأس المال العامل الشهري
        working_summary_frame = tk.Frame(costs_frame, bg="#f8f9fa")
        working_summary_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(working_summary_frame, text="رأس المال العامل (شهري):",
                font=("Tajawal", 10), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=5)

        self.summary_working_label = tk.Label(working_summary_frame, text="0.00 د.ع",
                                            font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#e74c3c")
        self.summary_working_label.pack(side="right")

        # الإيرادات
        revenues_frame = tk.LabelFrame(summary_frame, text="الإيرادات", font=("Tajawal", 11, "bold"),
                                     bg="#f8f9fa", fg="#27ae60")
        revenues_frame.pack(fill="x", padx=10, pady=5)

        # الأرباح الشهرية
        monthly_revenue_frame = tk.Frame(revenues_frame, bg="#f8f9fa")
        monthly_revenue_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(monthly_revenue_frame, text="الأرباح الشهرية:",
                font=("Tajawal", 10), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=5)

        self.summary_monthly_revenue_label = tk.Label(monthly_revenue_frame, text="0.00 د.ع",
                                                    font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#27ae60")
        self.summary_monthly_revenue_label.pack(side="right")

        # الإيرادات السنوية
        yearly_revenue_frame = tk.Frame(revenues_frame, bg="#f8f9fa")
        yearly_revenue_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(yearly_revenue_frame, text="الإيرادات السنوية:",
                font=("Tajawal", 10), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=5)

        self.summary_yearly_revenue_label = tk.Label(yearly_revenue_frame, text="0.00 د.ع",
                                                   font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#27ae60")
        self.summary_yearly_revenue_label.pack(side="right")

        # النتيجة النهائية
        result_frame = tk.LabelFrame(summary_frame, text="النتيجة النهائية", font=("Tajawal", 11, "bold"),
                                   bg="#f8f9fa", fg="#3498db")
        result_frame.pack(fill="x", padx=10, pady=5)

        # فترة الاسترداد
        payback_frame = tk.Frame(result_frame, bg="#f8f9fa")
        payback_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(payback_frame, text="فترة استرداد رأس المال:",
                font=("Tajawal", 10), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=5)

        self.payback_period_label = tk.Label(payback_frame, text="-- شهر",
                                           font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#3498db")
        self.payback_period_label.pack(side="right")

        # معدل العائد
        roi_frame = tk.Frame(result_frame, bg="#f8f9fa")
        roi_frame.pack(fill="x", padx=10, pady=2)

        tk.Label(roi_frame, text="معدل العائد السنوي:",
                font=("Tajawal", 10), bg="#f8f9fa", fg="#2c3e50").pack(side="right", padx=5)

        self.roi_label = tk.Label(roi_frame, text="--%",
                                font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#3498db")
        self.roi_label.pack(side="right")

    def add_financial_item(self, tree, item_type):
        """إضافة بند مالي جديد"""
        dialog = FinancialItemDialog(self, f"إضافة {item_type}", item_type)
        if dialog.result:
            name, amount = dialog.result
            tree.insert("", "end", values=(name, f"{amount:,.2f}"))
            self.calculate_all_totals()

    def edit_financial_item(self, tree):
        """تعديل بند مالي محدد"""
        selected = tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بند للتعديل")
            return

        item = selected[0]
        values = tree.item(item, "values")

        dialog = FinancialItemDialog(self, "تعديل البند", "تعديل",
                                   initial_values=(values[0], float(values[1].replace(",", ""))))
        if dialog.result:
            name, amount = dialog.result
            tree.item(item, values=(name, f"{amount:,.2f}"))
            self.calculate_all_totals()

    def delete_financial_item(self, tree):
        """حذف بند مالي محدد"""
        selected = tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بند للحذف")
            return

        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا البند؟"):
            tree.delete(selected[0])
            self.calculate_all_totals()

    def add_working_capital_item(self):
        """إضافة بند رأس مال عامل"""
        dialog = WorkingCapitalDialog(self, "إضافة بند رأس مال عامل")
        if dialog.result:
            name, item_type, amount = dialog.result
            self.working_tree.insert("", "end", values=(name, item_type, f"{amount:,.2f}"))
            self.calculate_all_totals()

    def edit_working_capital_item(self):
        """تعديل بند رأس مال عامل"""
        selected = self.working_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بند للتعديل")
            return

        item = selected[0]
        values = self.working_tree.item(item, "values")

        dialog = WorkingCapitalDialog(self, "تعديل بند رأس المال العامل",
                                    initial_values=(values[0], values[1], float(values[2].replace(",", ""))))
        if dialog.result:
            name, item_type, amount = dialog.result
            self.working_tree.item(item, values=(name, item_type, f"{amount:,.2f}"))
            self.calculate_all_totals()

    def add_product_profit(self):
        """إضافة منتج للأرباح الشهرية"""
        dialog = ProductProfitDialog(self, "إضافة منتج/خدمة")
        if dialog.result:
            name, units, unit_cost, selling_price = dialog.result
            total_cost = units * unit_cost
            total_revenue = units * selling_price
            total_profit = total_revenue - total_cost

            self.profits_tree.insert("", "end", values=(
                name, units, f"{unit_cost:,.2f}", f"{total_cost:,.2f}",
                f"{selling_price:,.2f}", f"{total_revenue:,.2f}", f"{total_profit:,.2f}"
            ))
            self.calculate_all_totals()

    def edit_product_profit(self):
        """تعديل منتج في الأرباح الشهرية"""
        selected = self.profits_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
            return

        item = selected[0]
        values = self.profits_tree.item(item, "values")

        dialog = ProductProfitDialog(self, "تعديل المنتج/الخدمة",
                                   initial_values=(values[0], int(values[1]),
                                                 float(values[2].replace(",", "")),
                                                 float(values[4].replace(",", ""))))
        if dialog.result:
            name, units, unit_cost, selling_price = dialog.result
            total_cost = units * unit_cost
            total_revenue = units * selling_price
            total_profit = total_revenue - total_cost

            self.profits_tree.item(item, values=(
                name, units, f"{unit_cost:,.2f}", f"{total_cost:,.2f}",
                f"{selling_price:,.2f}", f"{total_revenue:,.2f}", f"{total_profit:,.2f}"
            ))
            self.calculate_all_totals()

    def add_monthly_revenue(self):
        """إضافة إيرادات شهرية"""
        dialog = MonthlyRevenueDialog(self, "إضافة إيرادات شهرية")
        if dialog.result:
            month, product1, product2, product3, product4 = dialog.result
            total = product1 + product2 + product3 + product4

            self.yearly_tree.insert("", "end", values=(
                month, f"{product1:,.2f}", f"{product2:,.2f}",
                f"{product3:,.2f}", f"{product4:,.2f}", f"{total:,.2f}"
            ))
            self.calculate_all_totals()

    def edit_monthly_revenue(self):
        """تعديل إيرادات شهرية"""
        selected = self.yearly_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار شهر للتعديل")
            return

        item = selected[0]
        values = self.yearly_tree.item(item, "values")

        dialog = MonthlyRevenueDialog(self, "تعديل الإيرادات الشهرية",
                                    initial_values=(values[0],
                                                  float(values[1].replace(",", "")),
                                                  float(values[2].replace(",", "")),
                                                  float(values[3].replace(",", "")),
                                                  float(values[4].replace(",", ""))))
        if dialog.result:
            month, product1, product2, product3, product4 = dialog.result
            total = product1 + product2 + product3 + product4

            self.yearly_tree.item(item, values=(
                month, f"{product1:,.2f}", f"{product2:,.2f}",
                f"{product3:,.2f}", f"{product4:,.2f}", f"{total:,.2f}"
            ))
            self.calculate_all_totals()

    def fill_all_months(self):
        """ملء جميع أشهر السنة"""
        months = ["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                 "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"]

        # مسح البيانات الحالية
        for item in self.yearly_tree.get_children():
            self.yearly_tree.delete(item)

        # إضافة جميع الأشهر
        for month in months:
            self.yearly_tree.insert("", "end", values=(month, "0.00", "0.00", "0.00", "0.00", "0.00"))

        self.calculate_all_totals()

    def calculate_all_totals(self):
        """حساب جميع الإجماليات"""
        # حساب تكاليف التأسيس
        startup_total = 0
        for item in self.startup_tree.get_children():
            values = self.startup_tree.item(item, "values")
            startup_total += float(values[1].replace(",", ""))

        # حساب رأس المال الثابت
        fixed_total = 0
        for item in self.fixed_tree.get_children():
            values = self.fixed_tree.item(item, "values")
            fixed_total += float(values[1].replace(",", ""))

        # حساب رأس المال العامل
        working_total = 0
        for item in self.working_tree.get_children():
            values = self.working_tree.item(item, "values")
            working_total += float(values[2].replace(",", ""))

        # حساب الأرباح الشهرية
        profits_total = 0
        for item in self.profits_tree.get_children():
            values = self.profits_tree.item(item, "values")
            profits_total += float(values[6].replace(",", ""))

        # حساب الإيرادات السنوية
        yearly_total = 0
        for item in self.yearly_tree.get_children():
            values = self.yearly_tree.item(item, "values")
            yearly_total += float(values[5].replace(",", ""))

        # تحديث التسميات بالعملة المزدوجة
        self.startup_total_label.config(text=modern_styles.format_currency_dual(startup_total))
        self.fixed_total_label.config(text=modern_styles.format_currency_dual(fixed_total))
        self.working_total_label.config(text=modern_styles.format_currency_dual(working_total))
        self.profits_total_label.config(text=modern_styles.format_currency_dual(profits_total))
        self.yearly_total_label.config(text=modern_styles.format_currency_dual(yearly_total))

        # تحديث الملخص بالعملة المزدوجة
        self.summary_startup_label.config(text=modern_styles.format_currency_dual(startup_total))
        self.summary_fixed_label.config(text=modern_styles.format_currency_dual(fixed_total))
        self.summary_working_label.config(text=modern_styles.format_currency_dual(working_total))
        self.summary_monthly_revenue_label.config(text=modern_styles.format_currency_dual(profits_total))
        self.summary_yearly_revenue_label.config(text=modern_styles.format_currency_dual(yearly_total))

        # حساب فترة الاسترداد ومعدل العائد
        total_investment = startup_total + fixed_total
        if profits_total > 0 and total_investment > 0:
            payback_months = total_investment / profits_total
            roi = (yearly_total / total_investment) * 100 if total_investment > 0 else 0

            self.payback_period_label.config(text=f"{payback_months:.1f} شهر")
            self.roi_label.config(text=f"{roi:.1f}%")
        else:
            self.payback_period_label.config(text="-- شهر")
            self.roi_label.config(text="--%")

    def financial_analysis(self):
        """إجراء تحليل مالي شامل"""
        analysis = "التحليل المالي الشامل:\n\n"

        # جمع البيانات
        startup_total = sum(float(self.startup_tree.item(item, "values")[1].replace(",", ""))
                          for item in self.startup_tree.get_children())
        fixed_total = sum(float(self.fixed_tree.item(item, "values")[1].replace(",", ""))
                        for item in self.fixed_tree.get_children())
        working_total = sum(float(self.working_tree.item(item, "values")[2].replace(",", ""))
                          for item in self.working_tree.get_children())
        profits_total = sum(float(self.profits_tree.item(item, "values")[6].replace(",", ""))
                          for item in self.profits_tree.get_children())
        yearly_total = sum(float(self.yearly_tree.item(item, "values")[5].replace(",", ""))
                         for item in self.yearly_tree.get_children())

        total_investment = startup_total + fixed_total

        analysis += f"💰 إجمالي الاستثمار المطلوب: {modern_styles.format_currency_dual(total_investment)}\n"
        analysis += f"   - تكاليف التأسيس: {modern_styles.format_currency_dual(startup_total)}\n"
        analysis += f"   - رأس المال الثابت: {modern_styles.format_currency_dual(fixed_total)}\n\n"

        analysis += f"📊 التدفقات النقدية:\n"
        analysis += f"   - رأس المال العامل الشهري: {modern_styles.format_currency_dual(working_total)}\n"
        analysis += f"   - الأرباح الشهرية المتوقعة: {modern_styles.format_currency_dual(profits_total)}\n"
        analysis += f"   - الإيرادات السنوية المتوقعة: {modern_styles.format_currency_dual(yearly_total)}\n\n"

        if profits_total > 0 and total_investment > 0:
            payback_months = total_investment / profits_total
            roi = (yearly_total / total_investment) * 100

            analysis += f"📈 مؤشرات الأداء:\n"
            analysis += f"   - فترة استرداد رأس المال: {payback_months:.1f} شهر\n"
            analysis += f"   - معدل العائد السنوي: {roi:.1f}%\n\n"

            # تقييم الجدوى
            if roi >= 20:
                analysis += "✅ التقييم: مشروع مربح جداً وينصح بالاستثمار فيه\n"
            elif roi >= 15:
                analysis += "✅ التقييم: مشروع مربح وجيد للاستثمار\n"
            elif roi >= 10:
                analysis += "⚠️ التقييم: مشروع مقبول مع مخاطر متوسطة\n"
            else:
                analysis += "❌ التقييم: مشروع غير مربح ولا ينصح بالاستثمار\n"
        else:
            analysis += "⚠️ لا يمكن حساب مؤشرات الأداء - يرجى إدخال بيانات كاملة\n"

        # إظهار التحليل في نافذة منفصلة
        self.show_analysis_window("التحليل المالي الشامل", analysis)

    def show_analysis_window(self, title, content):
        """إظهار نافذة التحليل"""
        analysis_window = tk.Toplevel(self)
        analysis_window.title(title)
        analysis_window.geometry("600x500")
        analysis_window.configure(bg="#ffffff")

        tk.Label(analysis_window, text=title, font=("Tajawal", 16, "bold"),
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        text_frame = tk.Frame(analysis_window, bg="#ffffff")
        text_frame.pack(fill="both", expand=True, padx=20, pady=10)

        text_widget = tk.Text(text_frame, font=("Tajawal", 11), wrap="word",
                            relief="solid", bd=1)
        text_widget.pack(fill="both", expand=True)

        scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)
        scrollbar.pack(side="left", fill="y")

        text_widget.insert("1.0", content)
        text_widget.config(state="disabled")

        btn_frame = tk.Frame(analysis_window, bg="#ffffff")
        btn_frame.pack(fill="x", pady=20)

        tk.Button(btn_frame, text="📋 نسخ", font=("Tajawal", 11), bg="#3498db", fg="white",
                 command=lambda: self.copy_to_clipboard(content)).pack(side="right", padx=20)

        tk.Button(btn_frame, text="إغلاق", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=analysis_window.destroy).pack(side="right", padx=10)

    def copy_to_clipboard(self, content):
        """نسخ المحتوى إلى الحافظة"""
        self.clipboard_clear()
        self.clipboard_append(content)
        messagebox.showinfo("نجح", "تم نسخ التحليل إلى الحافظة")

    def save_data(self):
        """حفظ البيانات في نموذج البيانات"""
        # حفظ تكاليف التأسيس
        startup_costs = []
        for item in self.startup_tree.get_children():
            values = self.startup_tree.item(item, "values")
            startup_costs.append({
                "name": values[0],
                "amount": float(values[1].replace(",", ""))
            })

        # حفظ رأس المال الثابت
        fixed_capital = []
        for item in self.fixed_tree.get_children():
            values = self.fixed_tree.item(item, "values")
            fixed_capital.append({
                "name": values[0],
                "amount": float(values[1].replace(",", ""))
            })

        # حفظ رأس المال العامل
        working_capital = []
        for item in self.working_tree.get_children():
            values = self.working_tree.item(item, "values")
            working_capital.append({
                "name": values[0],
                "item_type": values[1],
                "amount": float(values[2].replace(",", ""))
            })

        # حفظ الأرباح الشهرية
        monthly_profits = []
        for item in self.profits_tree.get_children():
            values = self.profits_tree.item(item, "values")
            monthly_profits.append({
                "name": values[0],
                "units": int(values[1]),
                "unit_cost": float(values[2].replace(",", "")),
                "total_cost": float(values[3].replace(",", "")),
                "selling_price": float(values[4].replace(",", "")),
                "total_revenue": float(values[5].replace(",", "")),
                "total_profit": float(values[6].replace(",", ""))
            })

        # حفظ الإيرادات السنوية
        yearly_revenues = []
        for item in self.yearly_tree.get_children():
            values = self.yearly_tree.item(item, "values")
            yearly_revenues.append({
                "month": values[0],
                "product1": float(values[1].replace(",", "")),
                "product2": float(values[2].replace(",", "")),
                "product3": float(values[3].replace(",", "")),
                "product4": float(values[4].replace(",", "")),
                "total": float(values[5].replace(",", ""))
            })

        self.project_data.financials = {
            "startup_costs": startup_costs,
            "fixed_capital": fixed_capital,
            "working_capital": working_capital,
            "monthly_profits": monthly_profits,
            "yearly_revenues": yearly_revenues
        }

        # الانتقال إلى القسم التالي
        parent = self.master
        while parent and not hasattr(parent, 'go_to_next_section'):
            parent = parent.master
        if parent and hasattr(parent, 'go_to_next_section'):
            parent.go_to_next_section()

    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        # مسح البيانات الحالية
        for tree in [self.startup_tree, self.fixed_tree, self.working_tree,
                    self.profits_tree, self.yearly_tree]:
            for item in tree.get_children():
                tree.delete(item)

        financials = self.project_data.financials

        # تحميل تكاليف التأسيس
        for cost in financials.get("startup_costs", []):
            self.startup_tree.insert("", "end", values=(cost["name"], f"{cost['amount']:,.2f}"))

        # تحميل رأس المال الثابت
        for capital in financials.get("fixed_capital", []):
            self.fixed_tree.insert("", "end", values=(capital["name"], f"{capital['amount']:,.2f}"))

        # تحميل رأس المال العامل
        for working in financials.get("working_capital", []):
            self.working_tree.insert("", "end", values=(
                working["name"], working["item_type"], f"{working['amount']:,.2f}"))

        # تحميل الأرباح الشهرية
        for profit in financials.get("monthly_profits", []):
            self.profits_tree.insert("", "end", values=(
                profit["name"], profit["units"], f"{profit['unit_cost']:,.2f}",
                f"{profit['total_cost']:,.2f}", f"{profit['selling_price']:,.2f}",
                f"{profit['total_revenue']:,.2f}", f"{profit['total_profit']:,.2f}"
            ))

        # تحميل الإيرادات السنوية
        for revenue in financials.get("yearly_revenues", []):
            self.yearly_tree.insert("", "end", values=(
                revenue["month"], f"{revenue['product1']:,.2f}", f"{revenue['product2']:,.2f}",
                f"{revenue['product3']:,.2f}", f"{revenue['product4']:,.2f}", f"{revenue['total']:,.2f}"
            ))

        # إعادة حساب الإجماليات
        self.calculate_all_totals()

    def clear_data(self):
        """مسح جميع البيانات"""
        # مسح الجداول
        for tree in [self.startup_tree, self.fixed_tree, self.working_tree,
                    self.profits_tree, self.yearly_tree]:
            for item in tree.get_children():
                tree.delete(item)

        # إعادة إضافة البنود الافتراضية
        startup_items = ["رخصة المشروع", "توصيل ماء/كهرباء/هاتف", "رسوم خلو/نقل", "مصاريف أخرى"]
        for item in startup_items:
            self.startup_tree.insert("", "end", values=(item, "0.00"))

        fixed_items = ["تجهيز المحل", "الأثاث", "الآلات والمعدات", "نفقات أخرى"]
        for item in fixed_items:
            self.fixed_tree.insert("", "end", values=(item, "0.00"))

        working_items = [
            ("رواتب", "ثابت", "0.00"), ("إيجار", "ثابت", "0.00"), ("تسويق/دعاية", "ثابت", "0.00"),
            ("مواد خام", "متغير", "0.00"), ("أجور عمال", "متغير", "0.00"),
            ("فواتير/نقل", "متغير", "0.00"), ("أخرى", "", "0.00")
        ]
        for item in working_items:
            self.working_tree.insert("", "end", values=item)

        # إعادة تعيين الإجماليات
        self.calculate_all_totals()

        # مسح البيانات من النموذج
        self.project_data.financials = {
            "startup_costs": [],
            "fixed_capital": [],
            "working_capital": [],
            "monthly_profits": [],
            "yearly_revenues": []
        }


class FinancialItemDialog:
    """حوار إضافة/تعديل البنود المالية"""

    def __init__(self, parent, title, item_type, initial_values=None):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x250")
        self.dialog.configure(bg="#ffffff")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        tk.Label(self.dialog, text=title, font=("Tajawal", 14, "bold"),
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        form_frame = tk.Frame(self.dialog, bg="#ffffff")
        form_frame.pack(fill="both", expand=True, padx=30, pady=20)

        tk.Label(form_frame, text="اسم البند:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.name_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="right")
        self.name_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="المبلغ:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.amount_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.amount_entry.pack(fill="x", pady=5)

        if initial_values:
            self.name_entry.insert(0, initial_values[0])
            self.amount_entry.insert(0, str(initial_values[1]))

        buttons_frame = tk.Frame(self.dialog, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)

        tk.Button(buttons_frame, text="حفظ", font=("Tajawal", 11), bg="#27ae60", fg="white",
                 command=self.save, padx=20, pady=5).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="إلغاء", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=self.cancel, padx=20, pady=5).pack(side="right", padx=10)

        self.name_entry.focus()
        self.dialog.wait_window()

    def save(self):
        try:
            name = self.name_entry.get().strip()
            amount = float(self.amount_entry.get())

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم البند")
                return

            if amount < 0:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                return

            self.result = (name, amount)
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

    def cancel(self):
        self.dialog.destroy()


class WorkingCapitalDialog:
    """حوار إضافة/تعديل رأس المال العامل"""

    def __init__(self, parent, title, initial_values=None):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.configure(bg="#ffffff")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        tk.Label(self.dialog, text=title, font=("Tajawal", 14, "bold"),
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        form_frame = tk.Frame(self.dialog, bg="#ffffff")
        form_frame.pack(fill="both", expand=True, padx=30, pady=20)

        tk.Label(form_frame, text="اسم البند:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.name_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="right")
        self.name_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="نوع التكلفة:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.type_combo = ttk.Combobox(form_frame, font=("Tajawal", 11),
                                     values=["ثابت", "متغير"], state="readonly")
        self.type_combo.pack(fill="x", pady=5)

        tk.Label(form_frame, text="المبلغ الشهري:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.amount_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.amount_entry.pack(fill="x", pady=5)

        if initial_values:
            self.name_entry.insert(0, initial_values[0])
            self.type_combo.set(initial_values[1])
            self.amount_entry.insert(0, str(initial_values[2]))

        buttons_frame = tk.Frame(self.dialog, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)

        tk.Button(buttons_frame, text="حفظ", font=("Tajawal", 11), bg="#27ae60", fg="white",
                 command=self.save, padx=20, pady=5).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="إلغاء", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=self.cancel, padx=20, pady=5).pack(side="right", padx=10)

        self.name_entry.focus()
        self.dialog.wait_window()

    def save(self):
        try:
            name = self.name_entry.get().strip()
            item_type = self.type_combo.get()
            amount = float(self.amount_entry.get())

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم البند")
                return

            if amount < 0:
                messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")
                return

            self.result = (name, item_type, amount)
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال مبلغ صحيح")

    def cancel(self):
        self.dialog.destroy()


class ProductProfitDialog:
    """حوار إضافة/تعديل أرباح المنتجات"""

    def __init__(self, parent, title, initial_values=None):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x350")
        self.dialog.configure(bg="#ffffff")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        tk.Label(self.dialog, text=title, font=("Tajawal", 14, "bold"),
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        form_frame = tk.Frame(self.dialog, bg="#ffffff")
        form_frame.pack(fill="both", expand=True, padx=30, pady=20)

        tk.Label(form_frame, text="اسم المنتج/الخدمة:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.name_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="right")
        self.name_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="عدد الوحدات المباعة شهرياً:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.units_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.units_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="تكلفة الوحدة الواحدة:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.unit_cost_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.unit_cost_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="سعر بيع الوحدة الواحدة:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.selling_price_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.selling_price_entry.pack(fill="x", pady=5)

        # ربط الأحداث لحساب تلقائي
        for entry in [self.units_entry, self.unit_cost_entry, self.selling_price_entry]:
            entry.bind("<KeyRelease>", self.calculate_preview)

        # معاينة الحسابات
        self.preview_frame = tk.LabelFrame(form_frame, text="معاينة الحسابات",
                                         font=("Tajawal", 11, "bold"), bg="#f8f9fa")
        self.preview_frame.pack(fill="x", pady=10)

        self.total_cost_label = tk.Label(self.preview_frame, text="إجمالي التكلفة: 0.00",
                                       font=("Tajawal", 10), bg="#f8f9fa")
        self.total_cost_label.pack(anchor="e", padx=10, pady=2)

        self.total_revenue_label = tk.Label(self.preview_frame, text="إجمالي الإيراد: 0.00",
                                          font=("Tajawal", 10), bg="#f8f9fa")
        self.total_revenue_label.pack(anchor="e", padx=10, pady=2)

        self.total_profit_label = tk.Label(self.preview_frame, text="إجمالي الربح: 0.00",
                                         font=("Tajawal", 10, "bold"), bg="#f8f9fa", fg="#27ae60")
        self.total_profit_label.pack(anchor="e", padx=10, pady=2)

        if initial_values:
            self.name_entry.insert(0, initial_values[0])
            self.units_entry.insert(0, str(initial_values[1]))
            self.unit_cost_entry.insert(0, str(initial_values[2]))
            self.selling_price_entry.insert(0, str(initial_values[3]))
            self.calculate_preview()

        buttons_frame = tk.Frame(self.dialog, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)

        tk.Button(buttons_frame, text="حفظ", font=("Tajawal", 11), bg="#27ae60", fg="white",
                 command=self.save, padx=20, pady=5).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="إلغاء", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=self.cancel, padx=20, pady=5).pack(side="right", padx=10)

        self.name_entry.focus()
        self.dialog.wait_window()

    def calculate_preview(self, event=None):
        """حساب معاينة الأرباح"""
        try:
            units = int(self.units_entry.get() or 0)
            unit_cost = float(self.unit_cost_entry.get() or 0)
            selling_price = float(self.selling_price_entry.get() or 0)

            total_cost = units * unit_cost
            total_revenue = units * selling_price
            total_profit = total_revenue - total_cost

            self.total_cost_label.config(text=f"إجمالي التكلفة: {total_cost:,.2f}")
            self.total_revenue_label.config(text=f"إجمالي الإيراد: {total_revenue:,.2f}")
            self.total_profit_label.config(text=f"إجمالي الربح: {total_profit:,.2f}")

            # تغيير لون الربح حسب القيمة
            if total_profit > 0:
                self.total_profit_label.config(fg="#27ae60")
            elif total_profit < 0:
                self.total_profit_label.config(fg="#e74c3c")
            else:
                self.total_profit_label.config(fg="#95a5a6")

        except ValueError:
            self.total_cost_label.config(text="إجمالي التكلفة: --")
            self.total_revenue_label.config(text="إجمالي الإيراد: --")
            self.total_profit_label.config(text="إجمالي الربح: --")

    def save(self):
        try:
            name = self.name_entry.get().strip()
            units = int(self.units_entry.get())
            unit_cost = float(self.unit_cost_entry.get())
            selling_price = float(self.selling_price_entry.get())

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج/الخدمة")
                return

            if units <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال عدد وحدات صحيح أكبر من صفر")
                return

            if unit_cost < 0 or selling_price < 0:
                messagebox.showerror("خطأ", "يرجى إدخال أسعار صحيحة")
                return

            self.result = (name, units, unit_cost, selling_price)
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")

    def cancel(self):
        self.dialog.destroy()


class MonthlyRevenueDialog:
    """حوار إضافة/تعديل الإيرادات الشهرية"""

    def __init__(self, parent, title, initial_values=None):
        self.result = None

        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("450x400")
        self.dialog.configure(bg="#ffffff")
        self.dialog.transient(parent)
        self.dialog.grab_set()

        self.dialog.geometry("+%d+%d" % (parent.winfo_rootx() + 50, parent.winfo_rooty() + 50))

        tk.Label(self.dialog, text=title, font=("Tajawal", 14, "bold"),
                bg="#ffffff", fg="#1e3a8a").pack(pady=20)

        form_frame = tk.Frame(self.dialog, bg="#ffffff")
        form_frame.pack(fill="both", expand=True, padx=30, pady=20)

        tk.Label(form_frame, text="الشهر:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.month_combo = ttk.Combobox(form_frame, font=("Tajawal", 11),
                                      values=["يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
                                             "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"],
                                      state="readonly")
        self.month_combo.pack(fill="x", pady=5)

        tk.Label(form_frame, text="إيرادات المنتج الأول:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.product1_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.product1_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="إيرادات المنتج الثاني:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.product2_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.product2_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="إيرادات المنتج الثالث:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.product3_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.product3_entry.pack(fill="x", pady=5)

        tk.Label(form_frame, text="إيرادات المنتج الرابع:", font=("Tajawal", 12),
                bg="#ffffff", fg="#2c3e50").pack(anchor="e", pady=5)
        self.product4_entry = tk.Entry(form_frame, font=("Tajawal", 11), justify="center")
        self.product4_entry.pack(fill="x", pady=5)

        # ربط الأحداث لحساب تلقائي
        for entry in [self.product1_entry, self.product2_entry, self.product3_entry, self.product4_entry]:
            entry.bind("<KeyRelease>", self.calculate_total)

        # إجمالي الشهر
        self.total_label = tk.Label(form_frame, text="الإجمالي: 0.00",
                                  font=("Tajawal", 12, "bold"), bg="#ffffff", fg="#27ae60")
        self.total_label.pack(anchor="e", pady=10)

        if initial_values:
            self.month_combo.set(initial_values[0])
            self.product1_entry.insert(0, str(initial_values[1]))
            self.product2_entry.insert(0, str(initial_values[2]))
            self.product3_entry.insert(0, str(initial_values[3]))
            self.product4_entry.insert(0, str(initial_values[4]))
            self.calculate_total()

        buttons_frame = tk.Frame(self.dialog, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)

        tk.Button(buttons_frame, text="حفظ", font=("Tajawal", 11), bg="#27ae60", fg="white",
                 command=self.save, padx=20, pady=5).pack(side="right", padx=10)

        tk.Button(buttons_frame, text="إلغاء", font=("Tajawal", 11), bg="#95a5a6", fg="white",
                 command=self.cancel, padx=20, pady=5).pack(side="right", padx=10)

        self.month_combo.focus()
        self.dialog.wait_window()

    def calculate_total(self, event=None):
        """حساب إجمالي الشهر"""
        try:
            product1 = float(self.product1_entry.get() or 0)
            product2 = float(self.product2_entry.get() or 0)
            product3 = float(self.product3_entry.get() or 0)
            product4 = float(self.product4_entry.get() or 0)

            total = product1 + product2 + product3 + product4
            self.total_label.config(text=f"الإجمالي: {total:,.2f}")

        except ValueError:
            self.total_label.config(text="الإجمالي: --")

    def save(self):
        try:
            month = self.month_combo.get()
            product1 = float(self.product1_entry.get() or 0)
            product2 = float(self.product2_entry.get() or 0)
            product3 = float(self.product3_entry.get() or 0)
            product4 = float(self.product4_entry.get() or 0)

            if not month:
                messagebox.showerror("خطأ", "يرجى اختيار الشهر")
                return

            if all(x < 0 for x in [product1, product2, product3, product4]):
                messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للإيرادات")
                return

            self.result = (month, product1, product2, product3, product4)
            self.dialog.destroy()

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة")

    def cancel(self):
        self.dialog.destroy()
