import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import modern_styles
from tkinter import messagebox

class SWOTFrame(tk.Frame):
    """واجهة تحليل SWOT الرباعي"""
    
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg="#ffffff")
        title_frame.pack(fill="x", pady=(0, 20))
        
        tk.Label(title_frame, text="تحليل SWOT الرباعي", 
                font=("Tajawal", 18, "bold"), bg="#ffffff", fg="#1e3a8a").pack()
        
        # خط فاصل
        separator = tk.Frame(title_frame, height=2, bg="#4a90e2")
        separator.pack(fill="x", pady=10)
        
        # شرح تحليل SWOT
        info_frame = tk.Frame(self, bg="#e8f4fd", relief="solid", bd=1)
        info_frame.pack(fill="x", padx=40, pady=10)
        
        info_text = """
تحليل SWOT هو أداة تخطيط استراتيجي تساعد في تقييم:
• نقاط القوة (Strengths): العوامل الداخلية الإيجابية
• نقاط الضعف (Weaknesses): العوامل الداخلية السلبية  
• الفرص (Opportunities): العوامل الخارجية الإيجابية
• التهديدات (Threats): العوامل الخارجية السلبية
        """
        
        tk.Label(info_frame, text=info_text.strip(), font=("Tajawal", 10), 
                bg="#e8f4fd", fg="#2c3e50", justify="right").pack(pady=10, padx=20)
        
        # الشبكة الرئيسية لتحليل SWOT
        grid_frame = tk.Frame(self, bg="#ffffff")
        grid_frame.pack(fill="both", expand=True, padx=40, pady=20)
        
        # تكوين الشبكة
        grid_frame.grid_columnconfigure(0, weight=1)
        grid_frame.grid_columnconfigure(1, weight=1)
        grid_frame.grid_rowconfigure(0, weight=1)
        grid_frame.grid_rowconfigure(1, weight=1)
        
        # ألوان مختلفة لكل قسم
        colors = {
            "نقاط القوة": {"bg": "#d5f4e6", "header_bg": "#27ae60", "icon": "💪"},
            "نقاط الضعف": {"bg": "#ffeaa7", "header_bg": "#f39c12", "icon": "⚠️"},
            "الفرص": {"bg": "#dff9fb", "header_bg": "#00b894", "icon": "🎯"},
            "التهديدات": {"bg": "#fab1a0", "header_bg": "#e17055", "icon": "⚡"}
        }
        
        sections = [
            ("نقاط القوة", 0, 0),
            ("نقاط الضعف", 0, 1),
            ("الفرص", 1, 0),
            ("التهديدات", 1, 1)
        ]
        
        for section, row, col in sections:
            # إطار القسم
            section_frame = tk.Frame(grid_frame, bg=colors[section]["bg"], 
                                   relief="solid", bd=2)
            section_frame.grid(row=row, column=col, sticky="nsew", padx=5, pady=5)
            
            # عنوان القسم
            header_frame = tk.Frame(section_frame, bg=colors[section]["header_bg"])
            header_frame.pack(fill="x")
            
            tk.Label(header_frame, text=f"{colors[section]['icon']} {section}", 
                    font=("Tajawal", 14, "bold"), bg=colors[section]["header_bg"], 
                    fg="white", pady=10).pack()
            
            # منطقة النص
            text_frame = tk.Frame(section_frame, bg=colors[section]["bg"])
            text_frame.pack(fill="both", expand=True, padx=10, pady=10)
            
            # حقل النص مع دعم RTL
            text_widget = modern_styles.create_rtl_text(
                text_frame,
                font=modern_styles.get_safe_font("body"),
                height=8,
                bg=modern_styles.get_color("surface")
            )
            text_widget.pack(fill="both", expand=True)
            
            # شريط التمرير
            scrollbar = ttk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            scrollbar.pack(side="left", fill="y")
            
            # أمثلة توضيحية
            examples = self.get_examples(section)
            placeholder = f"أمثلة: {examples}"
            text_widget.insert("1.0", placeholder)
            text_widget.config(fg="gray")
            
            # ربط الأحداث
            text_widget.bind("<FocusIn>", lambda e, s=section: self.clear_placeholder(e, s))
            text_widget.bind("<FocusOut>", lambda e, s=section: self.add_placeholder(e, s))
            
            self.entries[section] = text_widget
        
        # قسم التحليل والاستراتيجيات
        analysis_frame = tk.LabelFrame(self, text="التحليل والاستراتيجيات المقترحة", 
                                     font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                     fg="#2c3e50", relief="solid", bd=1)
        analysis_frame.pack(fill="x", padx=40, pady=20)
        
        # استراتيجيات مقترحة
        strategies_frame = tk.Frame(analysis_frame, bg="#ffffff")
        strategies_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(strategies_frame, text="الاستراتيجيات المقترحة:", 
                font=("Tajawal", 12, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        strategies_text = modern_styles.create_rtl_text(
            strategies_frame,
            font=modern_styles.get_safe_font("body"),
            height=4,
            bg=modern_styles.get_color("background")
        )
        strategies_text.pack(fill="x", pady=5)
        self.entries["الاستراتيجيات المقترحة"] = strategies_text
        
        # أولويات التطوير
        priorities_frame = tk.Frame(analysis_frame, bg="#ffffff")
        priorities_frame.pack(fill="x", padx=15, pady=10)
        
        tk.Label(priorities_frame, text="أولويات التطوير:", 
                font=("Tajawal", 12, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        priorities_text = modern_styles.create_rtl_text(
            priorities_frame,
            font=modern_styles.get_safe_font("body"),
            height=3,
            bg=modern_styles.get_color("background")
        )
        priorities_text.pack(fill="x", pady=5)
        self.entries["أولويات التطوير"] = priorities_text
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)
        
        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat", 
                    "padx": 20, "pady": 8, "cursor": "hand2"}
        
        tk.Button(buttons_frame, text="💾 حفظ التحليل", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="📊 تحليل تلقائي", bg="#3498db", fg="white",
                 command=self.auto_analyze, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="📋 نسخ التحليل", bg="#9b59b6", fg="white",
                 command=self.copy_analysis, **btn_style).pack(side="right", padx=10)
    
    def get_examples(self, section):
        """الحصول على أمثلة توضيحية لكل قسم"""
        examples = {
            "نقاط القوة": "خبرة في المجال، موقع ممتاز، فريق مؤهل",
            "نقاط الضعف": "رأس مال محدود، قلة الخبرة التسويقية",
            "الفرص": "نمو السوق، دعم حكومي، تطور التكنولوجيا",
            "التهديدات": "منافسة شديدة، تغيرات اقتصادية، قوانين جديدة"
        }
        return examples.get(section, "")
    
    def clear_placeholder(self, event, section):
        """مسح النص التوضيحي عند التركيز"""
        text_widget = event.widget
        content = text_widget.get("1.0", tk.END).strip()
        examples = self.get_examples(section)
        
        if content.startswith("أمثلة:") or content == f"أمثلة: {examples}":
            text_widget.delete("1.0", tk.END)
            text_widget.config(fg="black")
    
    def add_placeholder(self, event, section):
        """إضافة النص التوضيحي عند فقدان التركيز"""
        text_widget = event.widget
        content = text_widget.get("1.0", tk.END).strip()
        
        if not content:
            examples = self.get_examples(section)
            text_widget.insert("1.0", f"أمثلة: {examples}")
            text_widget.config(fg="gray")
    
    def auto_analyze(self):
        """تحليل تلقائي وإقتراح استراتيجيات"""
        # جمع البيانات
        strengths = self.get_clean_text("نقاط القوة")
        weaknesses = self.get_clean_text("نقاط الضعف")
        opportunities = self.get_clean_text("الفرص")
        threats = self.get_clean_text("التهديدات")
        
        if not any([strengths, weaknesses, opportunities, threats]):
            messagebox.showwarning("تحذير", "يرجى إدخال بعض البيانات أولاً")
            return
        
        # إنشاء استراتيجيات مقترحة
        strategies = []
        
        if strengths and opportunities:
            strategies.append("• استراتيجية SO: استغلال نقاط القوة لاستثمار الفرص المتاحة")
        
        if weaknesses and opportunities:
            strategies.append("• استراتيجية WO: تطوير نقاط الضعف للاستفادة من الفرص")
        
        if strengths and threats:
            strategies.append("• استراتيجية ST: استخدام نقاط القوة لمواجهة التهديدات")
        
        if weaknesses and threats:
            strategies.append("• استراتيجية WT: تقليل نقاط الضعف وتجنب التهديدات")
        
        # تحديث حقل الاستراتيجيات
        strategies_text = self.entries["الاستراتيجيات المقترحة"]
        strategies_text.delete("1.0", tk.END)
        strategies_text.insert("1.0", "\n".join(strategies))
        
        # اقتراح أولويات
        priorities = []
        if weaknesses:
            priorities.append("• معالجة نقاط الضعف الحرجة")
        if opportunities:
            priorities.append("• استثمار الفرص المتاحة بسرعة")
        if threats:
            priorities.append("• وضع خطط للتعامل مع التهديدات")
        
        priorities_text = self.entries["أولويات التطوير"]
        priorities_text.delete("1.0", tk.END)
        priorities_text.insert("1.0", "\n".join(priorities))
        
        messagebox.showinfo("نجح", "تم إنشاء التحليل التلقائي والاستراتيجيات المقترحة")
    
    def copy_analysis(self):
        """نسخ التحليل الكامل إلى الحافظة"""
        analysis = "تحليل SWOT:\n\n"
        
        for section in ["نقاط القوة", "نقاط الضعف", "الفرص", "التهديدات"]:
            content = self.get_clean_text(section)
            if content:
                analysis += f"{section}:\n{content}\n\n"
        
        strategies = self.get_clean_text("الاستراتيجيات المقترحة")
        if strategies:
            analysis += f"الاستراتيجيات المقترحة:\n{strategies}\n\n"
        
        priorities = self.get_clean_text("أولويات التطوير")
        if priorities:
            analysis += f"أولويات التطوير:\n{priorities}\n"
        
        # نسخ إلى الحافظة
        self.clipboard_clear()
        self.clipboard_append(analysis)
        messagebox.showinfo("نجح", "تم نسخ التحليل إلى الحافظة")
    
    def get_clean_text(self, field):
        """الحصول على النص النظيف (بدون النص التوضيحي)"""
        if field not in self.entries:
            return ""
        
        text_widget = self.entries[field]
        content = text_widget.get("1.0", tk.END).strip()
        
        # إزالة النص التوضيحي
        if content.startswith("أمثلة:"):
            return ""
        
        return content
    
    def save_data(self):
        """حفظ البيانات في نموذج البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = self.get_clean_text(field)
                self.project_data.swot[field] = value
    
    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = self.project_data.swot.get(field, "")
                entry.delete("1.0", tk.END)
                if value:
                    entry.insert("1.0", value)
                    entry.config(fg="black")
                else:
                    # إضافة النص التوضيحي
                    if field in ["نقاط القوة", "نقاط الضعف", "الفرص", "التهديدات"]:
                        examples = self.get_examples(field)
                        entry.insert("1.0", f"أمثلة: {examples}")
                        entry.config(fg="gray")
    
    def clear_data(self):
        """مسح جميع البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
                # إعادة النص التوضيحي
                if field in ["نقاط القوة", "نقاط الضعف", "الفرص", "التهديدات"]:
                    examples = self.get_examples(field)
                    entry.insert("1.0", f"أمثلة: {examples}")
                    entry.config(fg="gray")
        
        # مسح البيانات من النموذج
        self.project_data.swot.clear()
