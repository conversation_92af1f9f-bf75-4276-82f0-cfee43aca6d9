"""
نماذج البيانات لنظام دراسة الجدوى الشامل
"""

class ProjectData:
    """فئة رئيسية لتخزين جميع بيانات المشروع"""
    
    def __init__(self):
        self.personal_info = {}
        self.project_info = {}
        self.market_analysis = {}
        self.swot = {
            "نقاط القوة": "",
            "نقاط الضعف": "",
            "الفرص": "",
            "التهديدات": ""
        }
        self.marketing_mix = {
            "المنتج": "",
            "السعر": "",
            "المكان": "",
            "الترويج": "",
            "الأشخاص": ""
        }
        self.production = {
            "equipment": [],  # المعدات والآلات
            "raw_materials": []  # المواد الخام
        }
        self.financials = {
            "startup_costs": [],  # تكاليف التأسيس
            "fixed_capital": [],  # رأس المال الثابت
            "working_capital": [],  # رأس المال العامل
            "monthly_profits": [],  # الأرباح الشهرية
            "yearly_revenues": []  # الإيرادات السنوية
        }
        self.summary = {}

        # معلومات إضافية
        self.metadata = {
            "created_date": "",
            "last_modified": "",
            "version": "1.0",
            "author": ""
        }
    
    def to_dict(self):
        """تحويل البيانات إلى قاموس للحفظ"""
        import datetime

        # تحديث تاريخ آخر تعديل
        self.metadata["last_modified"] = datetime.datetime.now().isoformat()
        if not self.metadata.get("created_date"):
            self.metadata["created_date"] = self.metadata["last_modified"]

        return {
            "personal_info": self.personal_info,
            "project_info": self.project_info,
            "market_analysis": self.market_analysis,
            "swot": self.swot,
            "marketing_mix": self.marketing_mix,
            "production": self.production,
            "financials": self.financials,
            "summary": self.summary,
            "metadata": self.metadata
        }
    
    def from_dict(self, data):
        """تحميل البيانات من قاموس"""
        self.personal_info = data.get("personal_info", {})
        self.project_info = data.get("project_info", {})
        self.market_analysis = data.get("market_analysis", {})
        self.swot = data.get("swot", {
            "نقاط القوة": "",
            "نقاط الضعف": "",
            "الفرص": "",
            "التهديدات": ""
        })
        self.marketing_mix = data.get("marketing_mix", {
            "المنتج": "",
            "السعر": "",
            "المكان": "",
            "الترويج": "",
            "الأشخاص": ""
        })
        self.production = data.get("production", {
            "equipment": [],
            "raw_materials": []
        })
        self.financials = data.get("financials", {
            "startup_costs": [],
            "fixed_capital": [],
            "working_capital": [],
            "monthly_profits": [],
            "yearly_revenues": []
        })
        self.summary = data.get("summary", {})
        self.metadata = data.get("metadata", {
            "created_date": "",
            "last_modified": "",
            "version": "1.0",
            "author": ""
        })

    def validate_data(self):
        """التحقق من صحة البيانات"""
        errors = []

        # التحقق من المعلومات الشخصية الأساسية
        required_personal = ["اسم صاحب المشروع", "العمر", "رقم الهاتف"]
        for field in required_personal:
            if not self.personal_info.get(field, "").strip():
                errors.append(f"المعلومات الشخصية: {field} مطلوب")

        # التحقق من معلومات المشروع الأساسية
        required_project = ["اسم المشروع", "موقع المشروع"]
        for field in required_project:
            if not self.project_info.get(field, "").strip():
                errors.append(f"بيانات المشروع: {field} مطلوب")

        # التحقق من وجود بيانات مالية أساسية
        if not self.financials.get("startup_costs") and not self.financials.get("fixed_capital"):
            errors.append("الدراسة المالية: يجب إدخال تكاليف التأسيس أو رأس المال الثابت على الأقل")

        return errors

    def get_summary_statistics(self):
        """الحصول على إحصائيات ملخصة للمشروع"""
        stats = {}

        # إحصائيات مالية
        if self.financials:
            startup_total = sum(item.get('amount', 0) for item in self.financials.get('startup_costs', []))
            fixed_total = sum(item.get('amount', 0) for item in self.financials.get('fixed_capital', []))
            working_total = sum(item.get('amount', 0) for item in self.financials.get('working_capital', []))

            stats['total_investment'] = startup_total + fixed_total
            stats['monthly_operating_cost'] = working_total
            stats['annual_operating_cost'] = working_total * 12

        # إحصائيات الإنتاج
        if self.production:
            stats['equipment_count'] = len(self.production.get('equipment', []))
            stats['raw_materials_count'] = len(self.production.get('raw_materials', []))

        # حالة اكتمال الأقسام
        completion = {}
        completion['personal_info'] = len([v for v in self.personal_info.values() if str(v).strip()]) > 0
        completion['project_info'] = len([v for v in self.project_info.values() if str(v).strip()]) > 0
        completion['market_analysis'] = len([v for v in self.market_analysis.values() if str(v).strip()]) > 0
        completion['swot'] = len([v for v in self.swot.values() if str(v).strip()]) > 0
        completion['marketing_mix'] = len([v for v in self.marketing_mix.values() if str(v).strip()]) > 0
        completion['production'] = len(self.production.get('equipment', [])) > 0 or len(self.production.get('raw_materials', [])) > 0
        completion['financials'] = any(len(v) > 0 for v in self.financials.values() if isinstance(v, list))
        completion['summary'] = len([v for v in self.summary.values() if str(v).strip()]) > 0

        stats['completion_rate'] = sum(completion.values()) / len(completion) * 100
        stats['completed_sections'] = sum(completion.values())
        stats['total_sections'] = len(completion)

        return stats

class EquipmentItem:
    """عنصر معدات أو آلة"""
    def __init__(self, name="", quantity=0, unit_price=0.0):
        self.name = name
        self.quantity = quantity
        self.unit_price = unit_price
    
    @property
    def total_price(self):
        return self.quantity * self.unit_price
    
    def to_dict(self):
        return {
            "name": self.name,
            "quantity": self.quantity,
            "unit_price": self.unit_price,
            "total_price": self.total_price
        }
    
    @classmethod
    def from_dict(cls, data):
        return cls(
            name=data.get("name", ""),
            quantity=data.get("quantity", 0),
            unit_price=data.get("unit_price", 0.0)
        )

class RawMaterial:
    """مادة خام"""
    def __init__(self, name="", quantity=0, unit_price=0.0):
        self.name = name
        self.quantity = quantity
        self.unit_price = unit_price
    
    @property
    def total_price(self):
        return self.quantity * self.unit_price
    
    def to_dict(self):
        return {
            "name": self.name,
            "quantity": self.quantity,
            "unit_price": self.unit_price,
            "total_price": self.total_price
        }
    
    @classmethod
    def from_dict(cls, data):
        return cls(
            name=data.get("name", ""),
            quantity=data.get("quantity", 0),
            unit_price=data.get("unit_price", 0.0)
        )

class FinancialItem:
    """عنصر مالي عام"""
    def __init__(self, name="", amount=0.0, item_type=""):
        self.name = name
        self.amount = amount
        self.item_type = item_type  # ثابت/متغير
    
    def to_dict(self):
        return {
            "name": self.name,
            "amount": self.amount,
            "item_type": self.item_type
        }
    
    @classmethod
    def from_dict(cls, data):
        return cls(
            name=data.get("name", ""),
            amount=data.get("amount", 0.0),
            item_type=data.get("item_type", "")
        )

class ProductProfit:
    """ربح منتج أو خدمة"""
    def __init__(self, name="", units=0, unit_cost=0.0, selling_price=0.0):
        self.name = name
        self.units = units
        self.unit_cost = unit_cost
        self.selling_price = selling_price
    
    @property
    def total_cost(self):
        return self.units * self.unit_cost
    
    @property
    def total_revenue(self):
        return self.units * self.selling_price
    
    @property
    def total_profit(self):
        return self.total_revenue - self.total_cost
    
    def to_dict(self):
        return {
            "name": self.name,
            "units": self.units,
            "unit_cost": self.unit_cost,
            "selling_price": self.selling_price,
            "total_cost": self.total_cost,
            "total_revenue": self.total_revenue,
            "total_profit": self.total_profit
        }
    
    @classmethod
    def from_dict(cls, data):
        return cls(
            name=data.get("name", ""),
            units=data.get("units", 0),
            unit_cost=data.get("unit_cost", 0.0),
            selling_price=data.get("selling_price", 0.0)
        )
