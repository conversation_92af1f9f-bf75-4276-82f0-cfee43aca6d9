#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص ملفات نظام دراسة الجدوى
"""

import os
import sys

def check_project_structure():
    """فحص هيكل المشروع"""
    print("🔍 فحص هيكل المشروع...")
    
    # الملفات الأساسية
    core_files = [
        "main.py",
        "run.py", 
        "config.py",
        "utils.py",
        "requirements.txt",
        "README.md"
    ]
    
    # ملفات البيانات
    data_files = [
        "data/__init__.py",
        "data/models.py",
        "data/data_manager.py",
        "data/export_manager.py"
    ]
    
    # ملفات الواجهات
    ui_files = [
        "ui/__init__.py",
        "ui/personal_info.py",
        "ui/project_info.py",
        "ui/market_analysis.py",
        "ui/swot.py",
        "ui/marketing_mix.py",
        "ui/production.py",
        "ui/financials.py",
        "ui/summary.py"
    ]
    
    # ملفات التوثيق
    doc_files = [
        "DEVELOPER_GUIDE.md",
        "PROJECT_SUMMARY.md",
        "CHANGELOG.md",
        "FINAL_SUMMARY.md",
        "LICENSE"
    ]
    
    all_files = core_files + data_files + ui_files + doc_files
    
    print(f"📋 فحص {len(all_files)} ملف...")
    
    missing_files = []
    existing_files = []
    
    for file_path in all_files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"❌ {file_path} - مفقود")
    
    print(f"\n📊 النتائج:")
    print(f"✅ ملفات موجودة: {len(existing_files)}")
    print(f"❌ ملفات مفقودة: {len(missing_files)}")
    
    if missing_files:
        print(f"\n📝 الملفات المفقودة:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    else:
        print(f"\n🎉 جميع الملفات موجودة!")
        return True

def check_file_sizes():
    """فحص أحجام الملفات"""
    print(f"\n🔍 فحص أحجام الملفات...")
    
    important_files = [
        "main.py",
        "data/models.py",
        "ui/personal_info.py",
        "ui/financials.py",
        "README.md"
    ]
    
    total_size = 0
    
    for file_path in important_files:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            print(f"📄 {file_path}: {size:,} بايت")
        else:
            print(f"❌ {file_path}: غير موجود")
    
    print(f"\n📊 إجمالي حجم الملفات المهمة: {total_size:,} بايت ({total_size/1024:.1f} KB)")
    
    return total_size > 0

def check_directories():
    """فحص المجلدات"""
    print(f"\n🔍 فحص المجلدات...")
    
    required_dirs = [
        "data",
        "ui", 
        "assets"
    ]
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            files_count = len([f for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))])
            print(f"✅ {dir_path}/ - {files_count} ملف")
        else:
            print(f"❌ {dir_path}/ - مفقود")

def check_syntax():
    """فحص صحة الكود Python"""
    print(f"\n🔍 فحص صحة الكود...")
    
    python_files = [
        "main.py",
        "config.py",
        "utils.py",
        "data/models.py",
        "data/data_manager.py",
        "ui/personal_info.py"
    ]
    
    syntax_errors = []
    
    for file_path in python_files:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    code = f.read()
                
                # محاولة تجميع الكود
                compile(code, file_path, 'exec')
                print(f"✅ {file_path} - صحيح")
                
            except SyntaxError as e:
                syntax_errors.append(f"{file_path}: {e}")
                print(f"❌ {file_path} - خطأ نحوي: {e}")
            except Exception as e:
                print(f"⚠️ {file_path} - تحذير: {e}")
        else:
            print(f"❌ {file_path} - غير موجود")
    
    if syntax_errors:
        print(f"\n❌ أخطاء نحوية:")
        for error in syntax_errors:
            print(f"   {error}")
        return False
    else:
        print(f"\n✅ جميع الملفات صحيحة نحوياً!")
        return True

def main():
    """الوظيفة الرئيسية"""
    print("=" * 60)
    print("🧪 فحص نظام دراسة الجدوى الشامل")
    print("=" * 60)
    
    # فحص هيكل المشروع
    structure_ok = check_project_structure()
    
    # فحص أحجام الملفات
    sizes_ok = check_file_sizes()
    
    # فحص المجلدات
    check_directories()
    
    # فحص صحة الكود
    syntax_ok = check_syntax()
    
    print("\n" + "=" * 60)
    print("📊 ملخص النتائج:")
    print(f"   📁 هيكل المشروع: {'✅ صحيح' if structure_ok else '❌ ناقص'}")
    print(f"   📄 أحجام الملفات: {'✅ طبيعية' if sizes_ok else '❌ مشكلة'}")
    print(f"   🐍 صحة الكود: {'✅ صحيح' if syntax_ok else '❌ أخطاء'}")
    
    overall_status = structure_ok and sizes_ok and syntax_ok
    
    if overall_status:
        print("\n🎉 المشروع جاهز ويبدو سليماً!")
        print("💡 يمكنك الآن تشغيل البرنامج باستخدام: python main.py")
    else:
        print("\n⚠️ توجد مشاكل تحتاج إلى إصلاح")
    
    print("=" * 60)
    
    return overall_status

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"\n❌ خطأ في الفحص: {e}")
        sys.exit(1)
