# نظام دراسة الجدوى الشامل

برنامج شامل لإعداد دراسات الجدوى الاقتصادية باللغة العربية مع واجهة مستخدم سهلة وميزات متقدمة.

## الميزات الرئيسية

### 🎯 أقسام شاملة
- **المعلومات الشخصية**: بيانات صاحب المشروع
- **بيانات المشروع**: معلومات أساسية ومتطلبات التمويل
- **دراسة السوق والمنافسين**: تحليل شامل للسوق
- **تحليل SWOT**: نقاط القوة والضعف والفرص والتهديدات
- **المزيج التسويقي**: استراتيجية التسويق (4Ps + People)
- **مستلزمات الإنتاج**: المعدات والمواد الخام
- **الدراسة المالية**: تحليل مالي شامل مع جداول تفاعلية
- **ملخص الأرباح**: نتائج نهائية ومؤشرات أداء

### 💾 إدارة البيانات
- حفظ وتحميل المشاريع بصيغة JSON
- حفظ تلقائي كل 5 دقائق
- نسخ احتياطية تلقائية
- التحقق من صحة البيانات
- إحصائيات شاملة للمشروع

### 📊 التصدير والطباعة
- تصدير إلى PDF مع تنسيق احترافي
- تصدير إلى Word (DOCX)
- تصدير البيانات المالية إلى Excel
- تقارير شاملة قابلة للطباعة

### 🧮 حسابات تلقائية
- حساب التكاليف الإجمالية
- حساب الأرباح والخسائر
- معدل العائد على الاستثمار (ROI)
- فترة استرداد رأس المال
- هامش الربح الصافي

### 🎨 واجهة مستخدم متقدمة
- تصميم عربي RTL احترافي
- ألوان متناسقة وسهولة في الاستخدام
- جداول تفاعلية لإدخال البيانات
- حوارات ذكية للإدخال والتعديل
- أيقونات وصور توضيحية

## متطلبات النظام

- Python 3.7 أو أحدث
- نظام التشغيل: Windows, macOS, Linux
- ذاكرة: 512 MB RAM كحد أدنى
- مساحة القرص: 100 MB

## التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone [repository-url]
cd saif
```

### 2. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

### 3. تشغيل البرنامج
```bash
python main.py
```

## المكتبات المطلوبة

- `reportlab`: لتصدير PDF
- `python-docx`: لتصدير Word
- `pandas`: لتحليل البيانات
- `openpyxl`: لتصدير Excel
- `Pillow`: لدعم الصور (اختياري)

## هيكل المشروع

```
saif/
├── main.py                 # الملف الرئيسي
├── ui/                     # واجهات المستخدم
│   ├── personal_info.py    # المعلومات الشخصية
│   ├── project_info.py     # بيانات المشروع
│   ├── market_analysis.py  # دراسة السوق
│   ├── swot.py            # تحليل SWOT
│   ├── marketing_mix.py    # المزيج التسويقي
│   ├── production.py       # مستلزمات الإنتاج
│   ├── financials.py       # الدراسة المالية
│   └── summary.py          # ملخص الأرباح
├── data/                   # إدارة البيانات
│   ├── models.py           # نماذج البيانات
│   ├── data_manager.py     # مدير البيانات
│   └── export_manager.py   # مدير التصدير
├── assets/                 # الأصول
│   └── README.md           # معلومات الأصول
├── requirements.txt        # المكتبات المطلوبة
└── README.md              # هذا الملف
```

## دليل الاستخدام السريع

### البدء
1. شغّل البرنامج باستخدام `python main.py`
2. اختر "مشروع جديد" أو افتح مشروع موجود
3. املأ البيانات في الأقسام المختلفة

### إدخال البيانات
- انقر على الأقسام في القائمة الجانبية
- املأ الحقول المطلوبة
- استخدم أزرار "حفظ البيانات" في كل قسم

### الحسابات المالية
- أدخل المعدات والمواد الخام في قسم "مستلزمات الإنتاج"
- أدخل التكاليف والإيرادات في "الدراسة المالية"
- راجع النتائج في "ملخص الأرباح"

### التصدير
- احفظ مشروعك أولاً
- اختر نوع التصدير من قائمة "ملف"
- ستجد الملفات في مجلد `exports/`

## الميزات المتقدمة

### التحقق من البيانات
- استخدم "التحقق من البيانات" للتأكد من اكتمال المعلومات
- يعرض البرنامج قائمة بالحقول المفقودة

### الإحصائيات
- اعرض إحصائيات شاملة عن مشروعك
- نسبة الاكتمال والمؤشرات المالية

### الحفظ التلقائي
- يحفظ البرنامج عملك تلقائياً كل 5 دقائق
- نسخ احتياطية تلقائية لحماية بياناتك

## استكشاف الأخطاء

### مشاكل التثبيت
```bash
# إذا واجهت مشاكل في تثبيت المكتبات
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

### مشاكل الخطوط العربية
- تأكد من وجود خطوط عربية مثبتة على النظام
- يمكن إضافة خطوط مخصصة في مجلد `assets/fonts/`

### مشاكل التصدير
- تأكد من تثبيت جميع المكتبات المطلوبة
- تحقق من صلاحيات الكتابة في مجلد التصدير

## المساهمة في التطوير

نرحب بمساهماتكم في تطوير البرنامج:

1. Fork المشروع
2. أنشئ branch جديد للميزة
3. اكتب الكود مع التوثيق
4. اختبر التغييرات
5. أرسل Pull Request

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

## الدعم والمساعدة

- استخدم دليل الاستخدام المدمج في البرنامج
- راجع هذا الملف للمعلومات التقنية
- أبلغ عن المشاكل في قسم Issues

## تحديثات مستقبلية

- دعم قواعد البيانات
- تحليلات مالية متقدمة
- قوالب جاهزة لأنواع مختلفة من المشاريع
- دعم اللغة الإنجليزية
- واجهة ويب

---

**تطوير**: نظام دراسة الجدوى الشامل  
**الإصدار**: 1.0  
**تاريخ التحديث**: 2024
