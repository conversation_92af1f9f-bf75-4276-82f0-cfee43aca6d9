import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import modern_styles

class ProjectInfoFrame(tk.Frame):
    """واجهة بيانات المشروع الأساسية"""
    
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg="#ffffff")
        title_frame.pack(fill="x", pady=(0, 20))
        
        tk.Label(title_frame, text="بيانات المشروع الأساسية", 
                font=("Tajawal", 18, "bold"), bg="#ffffff", fg="#1e3a8a").pack()
        
        # خط فاصل
        separator = tk.Frame(title_frame, height=2, bg="#4a90e2")
        separator.pack(fill="x", pady=10)
        
        # إطار النموذج الرئيسي
        main_form = tk.Frame(self, bg="#ffffff")
        main_form.pack(fill="both", expand=True, padx=40)
        
        # قسم المعلومات الأساسية
        basic_section = tk.LabelFrame(main_form, text="المعلومات الأساسية", 
                                    font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                    fg="#2c3e50", relief="solid", bd=1)
        basic_section.pack(fill="x", pady=10)
        
        basic_fields = [
            "اسم المشروع",
            "موقع المشروع",
            "أهمية الفكرة/نوع المشروع",
            "تاريخ تقديم الخطة"
        ]
        
        for i, field in enumerate(basic_fields):
            field_frame = tk.Frame(basic_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field, font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50", width=25, anchor="e").pack(side="right", padx=(0, 10))
            
            if field == "تاريخ تقديم الخطة":
                entry = modern_styles.create_rtl_entry(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    bg=modern_styles.get_color("background"),
                    fg="gray"
                )
                entry.insert(0, "YYYY-MM-DD")
                entry.bind("<FocusIn>", lambda e: self.clear_placeholder(e, "YYYY-MM-DD"))
            else:
                entry = modern_styles.create_rtl_entry(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    bg=modern_styles.get_color("background"),
                    fg=modern_styles.get_color("text_primary")
                )
            
            entry.pack(side="right", fill="x", expand=True)
            self.entries[field] = entry
        
        # قسم التمويل
        finance_section = tk.LabelFrame(main_form, text="معلومات التمويل", 
                                      font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                      fg="#2c3e50", relief="solid", bd=1)
        finance_section.pack(fill="x", pady=10)
        
        finance_fields = [
            "قيمة المنحة المطلوبة",
            "مصادر التمويل",
            "قيمة التمويل الذاتي",
            "تكلفة المشروع الكلية"
        ]
        
        for field in finance_fields:
            field_frame = tk.Frame(finance_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field, font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50", width=25, anchor="e").pack(side="right", padx=(0, 10))
            
            if "قيمة" in field or "تكلفة" in field:
                # حقول مالية مع تنسيق خاص ودعم RTL
                entry = modern_styles.create_rtl_entry(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    bg=modern_styles.get_color("background"),
                    fg=modern_styles.get_color("text_primary")
                )
                entry.bind("<KeyRelease>", lambda e: self.format_currency(e))
            else:
                entry = modern_styles.create_rtl_entry(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    bg=modern_styles.get_color("background"),
                    fg=modern_styles.get_color("text_primary")
                )
            
            entry.pack(side="right", fill="x", expand=True)
            self.entries[field] = entry
        
        # قسم التراخيص والمتطلبات
        license_section = tk.LabelFrame(main_form, text="التراخيص والمتطلبات", 
                                      font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                      fg="#2c3e50", relief="solid", bd=1)
        license_section.pack(fill="x", pady=10)
        
        # هل يحتاج المشروع لترخيص
        license_frame = tk.Frame(license_section, bg="#ffffff")
        license_frame.pack(fill="x", padx=15, pady=8)
        
        tk.Label(license_frame, text="هل يحتاج المشروع لترخيص؟", 
                font=("Tajawal", 12, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        license_var = tk.StringVar()
        license_radio_frame = tk.Frame(license_frame, bg="#ffffff")
        license_radio_frame.pack(anchor="e", pady=5)
        
        tk.Radiobutton(license_radio_frame, text="نعم", variable=license_var, value="نعم",
                      font=("Tajawal", 11), bg="#ffffff", 
                      command=lambda: self.toggle_license_authority(True)).pack(side="right", padx=10)
        tk.Radiobutton(license_radio_frame, text="لا", variable=license_var, value="لا",
                      font=("Tajawal", 11), bg="#ffffff",
                      command=lambda: self.toggle_license_authority(False)).pack(side="right", padx=10)
        
        self.entries["هل يحتاج المشروع لترخيص"] = license_var
        
        # جهة الترخيص
        authority_frame = tk.Frame(license_section, bg="#ffffff")
        authority_frame.pack(fill="x", padx=15, pady=8)
        
        tk.Label(authority_frame, text="جهة الترخيص (إن وجد):", 
                font=("Tajawal", 12, "bold"), bg="#ffffff", fg="#2c3e50", 
                width=25, anchor="e").pack(side="right", padx=(0, 10))
        
        authority_entry = modern_styles.create_rtl_entry(
            authority_frame,
            font=modern_styles.get_safe_font("body"),
            bg=modern_styles.get_color("background"),
            fg=modern_styles.get_color("text_primary"),
            state="disabled"
        )
        authority_entry.pack(side="right", fill="x", expand=True)
        self.entries["جهة الترخيص (إن وجد)"] = authority_entry
        
        # قسم الوصف والمهارات
        description_section = tk.LabelFrame(main_form, text="الوصف والمتطلبات", 
                                          font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                          fg="#2c3e50", relief="solid", bd=1)
        description_section.pack(fill="x", pady=10)
        
        text_fields = [
            "المهارات اللازمة",
            "حاجة المجتمع للمشروع",
            "الفئة المستهدفة"
        ]
        
        for field in text_fields:
            field_frame = tk.Frame(description_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field + ":", font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50").pack(anchor="e")
            
            text_widget = tk.Text(field_frame, font=("Tajawal", 11), height=3, 
                                relief="solid", bd=1, wrap="word")
            text_widget.pack(fill="x", pady=5)
            self.entries[field] = text_widget
        
        # أزرار العمليات
        buttons_frame = tk.Frame(self, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20)
        
        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat", 
                    "padx": 20, "pady": 8, "cursor": "hand2"}
        
        tk.Button(buttons_frame, text="💾 حفظ البيانات", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🧮 حساب التكلفة الإجمالية", bg="#3498db", fg="white",
                 command=self.calculate_total_cost, **btn_style).pack(side="right", padx=10)
    
    def clear_placeholder(self, event, placeholder):
        """مسح النص التوضيحي عند التركيز"""
        if event.widget.get() == placeholder:
            event.widget.delete(0, tk.END)
            event.widget.config(fg="black")
    
    def format_currency(self, event):
        """تنسيق الأرقام المالية"""
        try:
            value = event.widget.get().replace(",", "")
            if value and value.replace(".", "").isdigit():
                formatted = "{:,.0f}".format(float(value))
                event.widget.delete(0, tk.END)
                event.widget.insert(0, formatted)
        except:
            pass
    
    def toggle_license_authority(self, needs_license):
        """تفعيل/إلغاء تفعيل حقل جهة الترخيص"""
        authority_entry = self.entries["جهة الترخيص (إن وجد)"]
        if needs_license:
            authority_entry.config(state="normal")
        else:
            authority_entry.config(state="disabled")
            authority_entry.delete(0, tk.END)
    
    def calculate_total_cost(self):
        """حساب التكلفة الإجمالية"""
        try:
            grant = float(self.entries["قيمة المنحة المطلوبة"].get().replace(",", "") or 0)
            self_funding = float(self.entries["قيمة التمويل الذاتي"].get().replace(",", "") or 0)
            total = grant + self_funding
            
            self.entries["تكلفة المشروع الكلية"].delete(0, tk.END)
            self.entries["تكلفة المشروع الكلية"].insert(0, "{:,.0f}".format(total))
        except:
            pass
    
    def save_data(self):
        """حفظ البيانات في نموذج البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = entry.get("1.0", tk.END).strip()
            elif isinstance(entry, tk.StringVar):
                value = entry.get()
            else:
                value = entry.get().strip()
            self.project_data.project_info[field] = value
    
    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        for field, entry in self.entries.items():
            value = self.project_data.project_info.get(field, "")
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
                entry.insert("1.0", value)
            elif isinstance(entry, tk.StringVar):
                entry.set(value)
                # تحديث حالة حقل جهة الترخيص
                if field == "هل يحتاج المشروع لترخيص":
                    self.toggle_license_authority(value == "نعم")
            else:
                entry.delete(0, tk.END)
                entry.insert(0, value)
    
    def clear_data(self):
        """مسح جميع البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
            elif isinstance(entry, tk.StringVar):
                entry.set("")
            else:
                entry.delete(0, tk.END)
        
        # مسح البيانات من النموذج
        self.project_data.project_info.clear()
