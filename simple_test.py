#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مبسط لنظام دراسة الجدوى
"""

import sys
import os

def test_basic_imports():
    """اختبار الاستيراد الأساسي"""
    print("🔍 اختبار الاستيراد الأساسي...")
    
    try:
        # اختبار استيراد نماذج البيانات
        from data.models import ProjectData, EquipmentItem, RawMaterial, FinancialItem
        print("✅ تم استيراد نماذج البيانات بنجاح")
        
        # اختبار إنشاء مشروع
        project = ProjectData()
        print("✅ تم إنشاء مشروع جديد بنجاح")
        
        # اختبار إضافة بيانات
        project.personal_info["اسم صاحب المشروع"] = "أحمد محمد"
        project.project_info["اسم المشروع"] = "مشروع تجريبي"
        print("✅ تم إضافة بيانات تجريبية بنجاح")
        
        # اختبار التحويل إلى قاموس
        data_dict = project.to_dict()
        print("✅ تم تحويل البيانات إلى قاموس بنجاح")
        
        # اختبار التحميل من قاموس
        new_project = ProjectData()
        new_project.from_dict(data_dict)
        print("✅ تم تحميل البيانات من قاموس بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_data_manager():
    """اختبار مدير البيانات"""
    print("\n🔍 اختبار مدير البيانات...")
    
    try:
        from data.data_manager import DataManager
        from data.models import ProjectData
        
        # إنشاء مدير البيانات
        manager = DataManager()
        print("✅ تم إنشاء مدير البيانات بنجاح")
        
        # إنشاء مشروع تجريبي
        project = ProjectData()
        project.personal_info["اسم صاحب المشروع"] = "سارة أحمد"
        project.project_info["اسم المشروع"] = "مشروع اختبار"
        print("✅ تم إنشاء مشروع تجريبي بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير البيانات: {e}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("\n🔍 اختبار هيكل الملفات...")
    
    required_files = [
        "main.py",
        "data/models.py",
        "data/data_manager.py",
        "ui/personal_info.py",
        "ui/project_info.py",
        "README.md"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {missing_files}")
        return False
    else:
        print("✅ جميع الملفات الأساسية موجودة")
        return True

def test_ui_imports():
    """اختبار استيراد واجهات المستخدم"""
    print("\n🔍 اختبار استيراد واجهات المستخدم...")
    
    try:
        # اختبار tkinter أولاً
        import tkinter as tk
        print("✅ tkinter متوفر")
        
        # اختبار استيراد الواجهات
        from ui.personal_info import PersonalInfoFrame
        from ui.project_info import ProjectInfoFrame
        from ui.market_analysis import MarketAnalysisFrame
        from ui.swot import SWOTFrame
        print("✅ تم استيراد واجهات المستخدم بنجاح")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استيراد الواجهات: {e}")
        return False

def run_simple_tests():
    """تشغيل الاختبارات المبسطة"""
    print("=" * 50)
    print("🧪 اختبار مبسط لنظام دراسة الجدوى")
    print("=" * 50)
    
    tests = [
        ("هيكل الملفات", test_file_structure),
        ("الاستيراد الأساسي", test_basic_imports),
        ("مدير البيانات", test_data_manager),
        ("واجهات المستخدم", test_ui_imports)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}:")
        try:
            if test_func():
                passed += 1
                print(f"✅ نجح اختبار {test_name}")
            else:
                print(f"❌ فشل اختبار {test_name}")
        except Exception as e:
            print(f"❌ خطأ في اختبار {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! النظام جاهز للاستخدام")
        return True
    else:
        print("⚠️ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه")
        return False

if __name__ == "__main__":
    try:
        success = run_simple_tests()
        print(f"\n🏁 انتهى الاختبار - النتيجة: {'نجح' if success else 'فشل'}")
    except Exception as e:
        print(f"\n❌ خطأ عام في الاختبار: {e}")
