import tkinter as tk
from tkinter import ttk
import sys
import os

# إضافة مسار المشروع
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
import modern_styles

class MarketAnalysisFrame(tk.Frame):
    """واجهة دراسة السوق والمنافسين"""
    
    def __init__(self, master, project_data):
        super().__init__(master, bg="#ffffff")
        self.project_data = project_data
        self.entries = {}
        self.setup_ui()
    
    def setup_ui(self):
        # العنوان الرئيسي
        title_frame = tk.Frame(self, bg="#ffffff")
        title_frame.pack(fill="x", pady=(0, 20))
        
        tk.Label(title_frame, text="دراسة السوق والمنافسين", 
                font=("Tajawal", 18, "bold"), bg="#ffffff", fg="#1e3a8a").pack()
        
        # خط فاصل
        separator = tk.Frame(title_frame, height=2, bg="#4a90e2")
        separator.pack(fill="x", pady=10)
        
        # إطار قابل للتمرير
        canvas = tk.Canvas(self, bg="#ffffff")
        scrollbar = ttk.Scrollbar(self, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg="#ffffff")
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # المحتوى الرئيسي
        main_form = tk.Frame(scrollable_frame, bg="#ffffff")
        main_form.pack(fill="both", expand=True, padx=40, pady=20)
        
        # قسم المنتجات والخدمات
        products_section = tk.LabelFrame(main_form, text="المنتجات والخدمات", 
                                       font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                       fg="#2c3e50", relief="solid", bd=1)
        products_section.pack(fill="x", pady=10)
        
        product_fields = ["المنتجات", "الخدمات", "تميز المنتج/الخدمة"]
        
        for field in product_fields:
            field_frame = tk.Frame(products_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field + ":", font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50").pack(anchor="e")
            
            if field == "تميز المنتج/الخدمة":
                text_widget = modern_styles.create_rtl_text(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    height=3,
                    bg=modern_styles.get_color("background")
                )
                text_widget.pack(fill="x", pady=5)
                self.entries[field] = text_widget
            else:
                entry = modern_styles.create_rtl_entry(
                    field_frame,
                    font=modern_styles.get_safe_font("body"),
                    bg=modern_styles.get_color("background")
                )
                entry.pack(fill="x", pady=5)
                self.entries[field] = entry
        
        # قسم تحليل المنافسين
        competitors_section = tk.LabelFrame(main_form, text="تحليل المنافسين", 
                                          font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                          fg="#2c3e50", relief="solid", bd=1)
        competitors_section.pack(fill="x", pady=10)
        
        # هل يوجد منافسون
        comp_frame = tk.Frame(competitors_section, bg="#ffffff")
        comp_frame.pack(fill="x", padx=15, pady=8)
        
        tk.Label(comp_frame, text="هل يوجد منافسون؟", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        comp_var = tk.StringVar()
        comp_radio_frame = tk.Frame(comp_frame, bg="#ffffff")
        comp_radio_frame.pack(anchor="e", pady=5)
        
        tk.Radiobutton(comp_radio_frame, text="نعم", variable=comp_var, value="نعم",
                      font=("Tajawal", 11), bg="#ffffff",
                      command=lambda: self.toggle_competitor_fields(True)).pack(side="right", padx=10)
        tk.Radiobutton(comp_radio_frame, text="لا", variable=comp_var, value="لا",
                      font=("Tajawal", 11), bg="#ffffff",
                      command=lambda: self.toggle_competitor_fields(False)).pack(side="right", padx=10)
        
        self.entries["هل يوجد منافسون؟"] = comp_var
        
        # عدد المنافسين
        num_comp_frame = tk.Frame(competitors_section, bg="#ffffff")
        num_comp_frame.pack(fill="x", padx=15, pady=8)
        
        tk.Label(num_comp_frame, text="عدد المنافسين:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50", width=20, anchor="e").pack(side="right", padx=(0, 10))
        
        num_comp_entry = tk.Entry(num_comp_frame, font=("Tajawal", 11), width=10, 
                                justify="center", relief="solid", bd=1, state="disabled")
        num_comp_entry.pack(side="right")
        self.entries["عدد المنافسين"] = num_comp_entry
        
        # حقول المنافسين
        competitor_fields = [
            "المنتجات المنافسة",
            "طرق المنافسة", 
            "قنوات بيع المنافسين",
            "مراقبة المنافسين (الزبائن/يومياً)"
        ]
        
        self.competitor_entries = {}
        for field in competitor_fields:
            field_frame = tk.Frame(competitors_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field + ":", font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50").pack(anchor="e")
            
            if field == "طرق المنافسة":
                text_widget = tk.Text(field_frame, font=("Tajawal", 11), height=3, 
                                    relief="solid", bd=1, wrap="word", state="disabled")
                text_widget.pack(fill="x", pady=5)
                self.entries[field] = text_widget
                self.competitor_entries[field] = text_widget
            else:
                entry = tk.Entry(field_frame, font=("Tajawal", 11), 
                               justify="right", relief="solid", bd=1, state="disabled")
                entry.pack(fill="x", pady=5)
                self.entries[field] = entry
                self.competitor_entries[field] = entry
        
        # قسم مقارنة الأسعار
        price_section = tk.LabelFrame(main_form, text="مقارنة الأسعار", 
                                    font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                    fg="#2c3e50", relief="solid", bd=1)
        price_section.pack(fill="x", pady=10)
        
        price_frame = tk.Frame(price_section, bg="#ffffff")
        price_frame.pack(fill="x", padx=15, pady=8)
        
        tk.Label(price_frame, text="مقارنة أسعارك مع المنافسين:", 
                font=("Tajawal", 12, "bold"), bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        price_var = tk.StringVar()
        price_radio_frame = tk.Frame(price_frame, bg="#ffffff")
        price_radio_frame.pack(anchor="e", pady=5)
        
        price_options = ["أعلى من المنافسين", "مساوية للمنافسين", "أقل من المنافسين"]
        for option in price_options:
            tk.Radiobutton(price_radio_frame, text=option, variable=price_var, value=option,
                          font=("Tajawal", 10), bg="#ffffff").pack(anchor="e", pady=2)
        
        self.entries["مقارنة الأسعار"] = price_var
        
        # قسم السوق والطلب
        market_section = tk.LabelFrame(main_form, text="تحليل السوق والطلب", 
                                     font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                     fg="#2c3e50", relief="solid", bd=1)
        market_section.pack(fill="x", pady=10)
        
        market_fields = [
            "عدد الزبائن المحتملين",
            "معدل الاستهلاك الشهري",
            "حالة العرض والطلب",
            "مواسم الذروة"
        ]
        
        for field in market_fields:
            field_frame = tk.Frame(market_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field + ":", font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50", width=25, anchor="e").pack(side="right", padx=(0, 10))
            
            entry = tk.Entry(field_frame, font=("Tajawal", 11), 
                           justify="right", relief="solid", bd=1)
            entry.pack(side="right", fill="x", expand=True)
            self.entries[field] = entry
        
        # قسم التسويق والموردين
        marketing_section = tk.LabelFrame(main_form, text="التسويق والموردين", 
                                        font=("Tajawal", 12, "bold"), bg="#ffffff", 
                                        fg="#2c3e50", relief="solid", bd=1)
        marketing_section.pack(fill="x", pady=10)
        
        # خطة التسويق
        marketing_frame = tk.Frame(marketing_section, bg="#ffffff")
        marketing_frame.pack(fill="x", padx=15, pady=8)
        
        tk.Label(marketing_frame, text="خطة التسويق/البيع:", font=("Tajawal", 12, "bold"), 
                bg="#ffffff", fg="#2c3e50").pack(anchor="e")
        
        marketing_text = tk.Text(marketing_frame, font=("Tajawal", 11), height=4, 
                               relief="solid", bd=1, wrap="word")
        marketing_text.pack(fill="x", pady=5)
        self.entries["خطة التسويق/البيع"] = marketing_text
        
        # الموردين
        supplier_fields = [
            "الحاجة لموردين (عددهم)",
            "سهولة الوصول/أسعار الموردين"
        ]
        
        for field in supplier_fields:
            field_frame = tk.Frame(marketing_section, bg="#ffffff")
            field_frame.pack(fill="x", padx=15, pady=8)
            
            tk.Label(field_frame, text=field + ":", font=("Tajawal", 12, "bold"), 
                    bg="#ffffff", fg="#2c3e50", width=30, anchor="e").pack(side="right", padx=(0, 10))
            
            if "سهولة" in field:
                text_widget = tk.Text(field_frame, font=("Tajawal", 11), height=2, 
                                    relief="solid", bd=1, wrap="word")
                text_widget.pack(side="right", fill="x", expand=True)
                self.entries[field] = text_widget
            else:
                entry = tk.Entry(field_frame, font=("Tajawal", 11), 
                               justify="right", relief="solid", bd=1)
                entry.pack(side="right", fill="x", expand=True)
                self.entries[field] = entry
        
        # أزرار العمليات
        buttons_frame = tk.Frame(scrollable_frame, bg="#ffffff")
        buttons_frame.pack(fill="x", pady=20, padx=40)
        
        btn_style = {"font": ("Tajawal", 11, "bold"), "relief": "flat", 
                    "padx": 20, "pady": 8, "cursor": "hand2"}
        
        tk.Button(buttons_frame, text="💾 حفظ البيانات", bg="#27ae60", fg="white",
                 command=self.save_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="🗑️ مسح الكل", bg="#e74c3c", fg="white",
                 command=self.clear_data, **btn_style).pack(side="right", padx=10)
        
        tk.Button(buttons_frame, text="📊 تحليل السوق", bg="#9b59b6", fg="white",
                 command=self.analyze_market, **btn_style).pack(side="right", padx=10)
    
    def toggle_competitor_fields(self, has_competitors):
        """تفعيل/إلغاء تفعيل حقول المنافسين"""
        state = "normal" if has_competitors else "disabled"
        
        self.entries["عدد المنافسين"].config(state=state)
        if not has_competitors:
            self.entries["عدد المنافسين"].delete(0, tk.END)
        
        for field, entry in self.competitor_entries.items():
            entry.config(state=state)
            if not has_competitors:
                if isinstance(entry, tk.Text):
                    entry.delete("1.0", tk.END)
                else:
                    entry.delete(0, tk.END)
    
    def analyze_market(self):
        """تحليل بيانات السوق وإظهار ملخص"""
        # جمع البيانات الأساسية
        customers = self.entries["عدد الزبائن المحتملين"].get()
        consumption = self.entries["معدل الاستهلاك الشهري"].get()
        competitors = self.entries["هل يوجد منافسون؟"].get()
        
        analysis = "تحليل السوق:\n\n"
        
        if customers:
            analysis += f"• عدد الزبائن المحتملين: {customers}\n"
        
        if consumption:
            analysis += f"• معدل الاستهلاك الشهري: {consumption}\n"
        
        if competitors:
            analysis += f"• وجود منافسين: {competitors}\n"
            if competitors == "نعم":
                num_comp = self.entries["عدد المنافسين"].get()
                if num_comp:
                    analysis += f"• عدد المنافسين: {num_comp}\n"
        
        # إظهار النتيجة في نافذة منفصلة
        analysis_window = tk.Toplevel(self)
        analysis_window.title("تحليل السوق")
        analysis_window.geometry("500x400")
        analysis_window.configure(bg="#ffffff")
        
        tk.Label(analysis_window, text="نتائج تحليل السوق", 
                font=("Tajawal", 16, "bold"), bg="#ffffff", fg="#1e3a8a").pack(pady=20)
        
        text_widget = tk.Text(analysis_window, font=("Tajawal", 12), wrap="word", 
                            relief="solid", bd=1)
        text_widget.pack(fill="both", expand=True, padx=20, pady=20)
        text_widget.insert("1.0", analysis)
        text_widget.config(state="disabled")
    
    def save_data(self):
        """حفظ البيانات في نموذج البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                value = entry.get("1.0", tk.END).strip()
            elif isinstance(entry, tk.StringVar):
                value = entry.get()
            else:
                value = entry.get().strip()
            self.project_data.market_analysis[field] = value
    
    def load_data(self):
        """تحميل البيانات من نموذج البيانات"""
        for field, entry in self.entries.items():
            value = self.project_data.market_analysis.get(field, "")
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
                entry.insert("1.0", value)
            elif isinstance(entry, tk.StringVar):
                entry.set(value)
                # تحديث حالة حقول المنافسين
                if field == "هل يوجد منافسون؟":
                    self.toggle_competitor_fields(value == "نعم")
            else:
                entry.delete(0, tk.END)
                entry.insert(0, value)
    
    def clear_data(self):
        """مسح جميع البيانات"""
        for field, entry in self.entries.items():
            if isinstance(entry, tk.Text):
                entry.delete("1.0", tk.END)
            elif isinstance(entry, tk.StringVar):
                entry.set("")
            else:
                entry.delete(0, tk.END)
        
        # مسح البيانات من النموذج
        self.project_data.market_analysis.clear()
