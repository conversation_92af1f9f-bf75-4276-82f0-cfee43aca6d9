#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أنماط التصميم الحديث لنظام دراسة الجدوى
"""

# ألوان التصميم الحديث
MODERN_COLORS = {
    # الألوان الأساسية
    "primary": "#3b82f6",      # أزرق حديث
    "primary_dark": "#1e40af", # أزرق داكن
    "primary_light": "#dbeafe", # أزرق فاتح
    
    # الألوان الثانوية
    "secondary": "#10b981",    # أخضر حديث
    "accent": "#f59e0b",       # برتقالي حديث
    "danger": "#ef4444",       # أحمر حديث
    "warning": "#f59e0b",      # تحذير
    "success": "#10b981",      # نجاح
    "info": "#06b6d4",         # معلومات
    
    # ألوان الخلفية
    "background": "#f8fafc",   # خلفية رئيسية
    "surface": "#ffffff",      # سطح أبيض
    "card": "#ffffff",         # بطاقات
    "sidebar": "#f1f5f9",      # قائمة جانبية
    
    # ألوان النص
    "text_primary": "#1e293b", # نص رئيسي
    "text_secondary": "#64748b", # نص ثانوي
    "text_muted": "#94a3b8",   # نص خافت
    "text_white": "#ffffff",   # نص أبيض
    
    # ألوان الحدود والظلال
    "border": "#e2e8f0",       # حدود
    "border_light": "#f1f5f9", # حدود فاتحة
    "shadow": "#e2e8f0",       # ظل
    "shadow_dark": "#cbd5e1",  # ظل داكن
    
    # تدرجات ملونة للبطاقات
    "gradient_blue": "#3b82f6",
    "gradient_green": "#10b981", 
    "gradient_orange": "#f59e0b",
    "gradient_red": "#ef4444",
    "gradient_purple": "#8b5cf6",
    "gradient_cyan": "#06b6d4",
    "gradient_lime": "#84cc16",
    "gradient_pink": "#ec4899"
}

# خطوط التصميم الحديث مع دعم RTL
MODERN_FONTS = {
    "title": ("Noto Sans Arabic", 24, "bold"),      # عناوين رئيسية
    "heading": ("Noto Sans Arabic", 18, "bold"),    # عناوين فرعية
    "subheading": ("Noto Sans Arabic", 14, "bold"), # عناوين صغيرة
    "body": ("Noto Sans Arabic", 12),               # نص عادي
    "body_bold": ("Noto Sans Arabic", 12, "bold"),  # نص عادي عريض
    "small": ("Noto Sans Arabic", 10),              # نص صغير
    "caption": ("Noto Sans Arabic", 9),             # تسميات
    "button": ("Noto Sans Arabic", 11, "bold"),     # أزرار
    "icon": ("Arial", 16),                          # أيقونات
    "icon_large": ("Arial", 24),                    # أيقونات كبيرة

    # خطوط احتياطية في حالة عدم توفر Noto Sans Arabic
    "title_fallback": ("Tajawal", 24, "bold"),
    "heading_fallback": ("Tajawal", 18, "bold"),
    "body_fallback": ("Tajawal", 12),
    "button_fallback": ("Tajawal", 11, "bold"),
}

# أحجام وتباعدات
MODERN_SPACING = {
    "xs": 4,    # تباعد صغير جداً
    "sm": 8,    # تباعد صغير
    "md": 12,   # تباعد متوسط
    "lg": 16,   # تباعد كبير
    "xl": 20,   # تباعد كبير جداً
    "2xl": 24,  # تباعد ضخم
    "3xl": 32,  # تباعد ضخم جداً
}

# أنماط البطاقات
CARD_STYLES = {
    "default": {
        "bg": MODERN_COLORS["surface"],
        "relief": "flat",
        "bd": 0,
        "padx": 20,
        "pady": 20
    },
    "elevated": {
        "bg": MODERN_COLORS["surface"],
        "relief": "raised",
        "bd": 1,
        "padx": 20,
        "pady": 20
    },
    "outlined": {
        "bg": MODERN_COLORS["surface"],
        "relief": "solid",
        "bd": 1,
        "padx": 20,
        "pady": 20
    }
}

# أنماط الأزرار
BUTTON_STYLES = {
    "primary": {
        "bg": MODERN_COLORS["primary"],
        "fg": MODERN_COLORS["text_white"],
        "font": MODERN_FONTS["button"],
        "relief": "flat",
        "bd": 0,
        "padx": 20,
        "pady": 10,
        "cursor": "hand2"
    },
    "secondary": {
        "bg": MODERN_COLORS["secondary"],
        "fg": MODERN_COLORS["text_white"],
        "font": MODERN_FONTS["button"],
        "relief": "flat",
        "bd": 0,
        "padx": 20,
        "pady": 10,
        "cursor": "hand2"
    },
    "outline": {
        "bg": MODERN_COLORS["surface"],
        "fg": MODERN_COLORS["primary"],
        "font": MODERN_FONTS["button"],
        "relief": "solid",
        "bd": 2,
        "padx": 20,
        "pady": 10,
        "cursor": "hand2"
    },
    "ghost": {
        "bg": MODERN_COLORS["surface"],
        "fg": MODERN_COLORS["text_primary"],
        "font": MODERN_FONTS["button"],
        "relief": "flat",
        "bd": 0,
        "padx": 20,
        "pady": 10,
        "cursor": "hand2"
    }
}

# أنماط حقول الإدخال مع دعم RTL
INPUT_STYLES = {
    "default": {
        "font": MODERN_FONTS["body"],
        "bg": MODERN_COLORS["surface"],
        "fg": MODERN_COLORS["text_primary"],
        "relief": "solid",
        "bd": 1,
        "highlightthickness": 2,
        "highlightcolor": MODERN_COLORS["primary"],
        "highlightbackground": MODERN_COLORS["border"],
        "insertbackground": MODERN_COLORS["text_primary"],
        "justify": "right"   # محاذاة النص لليمين
    },
    "filled": {
        "font": MODERN_FONTS["body"],
        "bg": MODERN_COLORS["background"],
        "fg": MODERN_COLORS["text_primary"],
        "relief": "flat",
        "bd": 0,
        "highlightthickness": 2,
        "highlightcolor": MODERN_COLORS["primary"],
        "highlightbackground": MODERN_COLORS["border"],
        "insertbackground": MODERN_COLORS["text_primary"],
        "justify": "right"   # محاذاة النص لليمين
    },
    "rtl_entry": {
        "font": MODERN_FONTS["body"],
        "bg": MODERN_COLORS["surface"],
        "fg": MODERN_COLORS["text_primary"],
        "relief": "flat",
        "bd": 0,
        "highlightthickness": 2,
        "highlightcolor": MODERN_COLORS["primary"],
        "highlightbackground": MODERN_COLORS["border"],
        "insertbackground": MODERN_COLORS["text_primary"],
        "justify": "right"
    }
}

# أيقونات حديثة
MODERN_ICONS = {
    # أيقونات الأقسام
    "personal": "👤",
    "project": "🏢", 
    "market": "📊",
    "swot": "⚡",
    "marketing": "🎯",
    "production": "🏭",
    "financial": "💰",
    "summary": "📋",
    
    # أيقونات الإجراءات
    "save": "💾",
    "load": "📁",
    "new": "📄",
    "export": "📤",
    "import": "📥",
    "print": "🖨️",
    "edit": "✏️",
    "delete": "🗑️",
    "add": "➕",
    "remove": "➖",
    "check": "✅",
    "close": "❌",
    "info": "ℹ️",
    "warning": "⚠️",
    "error": "🚫",
    "success": "🎉",
    
    # أيقونات التنقل
    "next": "▶️",
    "previous": "◀️",
    "up": "🔼",
    "down": "🔽",
    "menu": "☰",
    "search": "🔍",
    "filter": "🔽",
    "sort": "🔄",
    
    # أيقونات الحالة
    "completed": "✅",
    "pending": "⏳",
    "in_progress": "🔄",
    "cancelled": "❌",
    "draft": "📝",
    
    # أيقونات متنوعة
    "star": "⭐",
    "heart": "❤️",
    "fire": "🔥",
    "rocket": "🚀",
    "sparkles": "✨",
    "trophy": "🏆",
    "target": "🎯",
    "chart": "📈",
    "calculator": "🧮",
    "calendar": "📅",
    "clock": "🕐",
    "location": "📍"
}

# بيانات العملات والإعدادات المحلية
CURRENCY_DATA = {
    "currencies": ["دينار عراقي", "دولار أمريكي"],
    "currency_symbols": {"دينار عراقي": "د.ع", "دولار أمريكي": "$"},
    "default_currency": "دينار عراقي",
    "exchange_rate": 1500,  # سعر صرف الدولار مقابل الدينار العراقي
    "show_dual_currency": True,  # عرض العملتين معاً
    "cost_types": ["ثابت", "متغير"]
}

# تأثيرات الحركة (للمحاكاة)
ANIMATION_EFFECTS = {
    "hover_scale": 1.05,       # تكبير عند التمرير
    "hover_opacity": 0.8,      # شفافية عند التمرير
    "transition_duration": 200, # مدة الانتقال بالميلي ثانية
    "bounce_scale": 1.1,       # تأثير الارتداد
    "fade_duration": 300,      # مدة التلاشي
}

# إعدادات الظلال
SHADOW_EFFECTS = {
    "light": {
        "color": MODERN_COLORS["shadow"],
        "offset_x": 2,
        "offset_y": 2,
        "blur": 4
    },
    "medium": {
        "color": MODERN_COLORS["shadow_dark"],
        "offset_x": 4,
        "offset_y": 4,
        "blur": 8
    },
    "heavy": {
        "color": MODERN_COLORS["shadow_dark"],
        "offset_x": 6,
        "offset_y": 6,
        "blur": 12
    }
}

# تدرجات الألوان
GRADIENTS = {
    "blue_gradient": ["#3b82f6", "#1e40af"],
    "green_gradient": ["#10b981", "#047857"],
    "orange_gradient": ["#f59e0b", "#d97706"],
    "red_gradient": ["#ef4444", "#dc2626"],
    "purple_gradient": ["#8b5cf6", "#7c3aed"],
    "cyan_gradient": ["#06b6d4", "#0891b2"],
    "background_gradient": ["#f8fafc", "#e2e8f0"]
}

# أنماط الرسوم البيانية
CHART_STYLES = {
    "colors": [
        MODERN_COLORS["primary"],
        MODERN_COLORS["secondary"],
        MODERN_COLORS["accent"],
        MODERN_COLORS["danger"],
        MODERN_COLORS["gradient_purple"],
        MODERN_COLORS["gradient_cyan"],
        MODERN_COLORS["gradient_lime"],
        MODERN_COLORS["gradient_pink"]
    ],
    "background": MODERN_COLORS["surface"],
    "grid_color": MODERN_COLORS["border"],
    "text_color": MODERN_COLORS["text_secondary"]
}

def get_color(color_name):
    """الحصول على لون من المجموعة"""
    return MODERN_COLORS.get(color_name, "#000000")

def get_font(font_name):
    """الحصول على خط من المجموعة"""
    return MODERN_FONTS.get(font_name, ("Arial", 12))

def get_icon(icon_name):
    """الحصول على أيقونة من المجموعة"""
    return MODERN_ICONS.get(icon_name, "📄")

def get_button_style(style_name):
    """الحصول على نمط زر"""
    return BUTTON_STYLES.get(style_name, BUTTON_STYLES["primary"])

def get_card_style(style_name):
    """الحصول على نمط بطاقة"""
    return CARD_STYLES.get(style_name, CARD_STYLES["default"])

def get_input_style(style_name):
    """الحصول على نمط حقل إدخال"""
    return INPUT_STYLES.get(style_name, INPUT_STYLES["default"])

def apply_hover_effect(widget, hover_color, normal_color):
    """تطبيق تأثير التمرير على عنصر"""
    def on_enter(event):
        widget.configure(bg=hover_color)
    
    def on_leave(event):
        widget.configure(bg=normal_color)
    
    widget.bind("<Enter>", on_enter)
    widget.bind("<Leave>", on_leave)

def create_gradient_frame(parent, colors, width=200, height=50):
    """إنشاء إطار بتدرج لوني (محاكاة بسيطة)"""
    import tkinter as tk

    frame = tk.Frame(parent, width=width, height=height)
    frame.pack_propagate(False)

    # محاكاة التدرج باستخدام عدة إطارات
    num_steps = 10
    step_height = height // num_steps

    for i in range(num_steps):
        # حساب اللون المتدرج (تبسيط)
        ratio = i / (num_steps - 1)
        if len(colors) >= 2:
            # استخدام اللون الأول والثاني
            color = colors[0] if ratio < 0.5 else colors[1]
        else:
            color = colors[0] if colors else "#ffffff"

        step_frame = tk.Frame(frame, bg=color, height=step_height)
        step_frame.pack(fill="x")

    return frame

def create_rtl_entry(parent, **kwargs):
    """إنشاء حقل إدخال مع دعم RTL"""
    import tkinter as tk

    # دمج الأنماط الافتراضية مع المخصصة
    style = get_input_style("rtl_entry").copy()
    style.update(kwargs)

    # إنشاء حقل الإدخال
    entry = tk.Entry(parent, **style)

    # تطبيق اتجاه RTL
    entry.configure(justify="right")

    return entry

def create_rtl_text(parent, **kwargs):
    """إنشاء مربع نص مع دعم RTL"""
    import tkinter as tk

    # الأنماط الافتراضية
    default_style = {
        "font": get_font("body"),
        "bg": get_color("surface"),
        "fg": get_color("text_primary"),
        "relief": "flat",
        "bd": 0,
        "highlightthickness": 2,
        "highlightcolor": get_color("primary"),
        "highlightbackground": get_color("border"),
        "insertbackground": get_color("text_primary"),
        "wrap": "word"
    }

    # دمج مع الأنماط المخصصة
    default_style.update(kwargs)

    # إنشاء مربع النص
    text_widget = tk.Text(parent, **default_style)

    # تطبيق اتجاه RTL
    text_widget.tag_configure("rtl", justify="right")
    text_widget.tag_add("rtl", "1.0", "end")

    return text_widget

def create_rtl_label(parent, text="", **kwargs):
    """إنشاء تسمية مع دعم RTL"""
    import tkinter as tk

    # الأنماط الافتراضية
    default_style = {
        "font": get_font("body"),
        "bg": get_color("surface"),
        "fg": get_color("text_primary"),
        "anchor": "e",  # محاذاة لليمين
        "justify": "right"
    }

    # دمج مع الأنماط المخصصة
    default_style.update(kwargs)

    # إنشاء التسمية
    label = tk.Label(parent, text=text, **default_style)

    return label

def create_rtl_combobox(parent, values=None, **kwargs):
    """إنشاء قائمة منسدلة مع دعم RTL"""
    import tkinter as tk
    from tkinter import ttk

    # الأنماط الافتراضية
    default_style = {
        "font": get_font("body"),
        "justify": "right",
        "state": "readonly"
    }

    # دمج مع الأنماط المخصصة
    default_style.update(kwargs)

    # إنشاء القائمة المنسدلة
    combobox = ttk.Combobox(parent, **default_style)

    if values:
        combobox['values'] = values

    return combobox

def get_safe_font(font_name):
    """الحصول على خط آمن مع احتياطي"""
    import tkinter as tk

    try:
        # محاولة استخدام الخط المطلوب
        font = get_font(font_name)
        # اختبار الخط
        test_label = tk.Label(None, font=font)
        test_label.destroy()
        return font
    except:
        # استخدام الخط الاحتياطي
        fallback_name = font_name + "_fallback"
        if fallback_name in MODERN_FONTS:
            return MODERN_FONTS[fallback_name]
        else:
            return ("Arial", 12)  # خط افتراضي آمن

def format_currency_dual(amount, show_usd=True):
    """تنسيق المبلغ بالعملتين (دينار عراقي ودولار أمريكي)"""
    try:
        amount = float(amount)
        exchange_rate = CURRENCY_DATA.get("exchange_rate", 1500)

        # تنسيق الدينار العراقي
        iqd_formatted = f"{amount:,.0f} د.ع"

        if show_usd and CURRENCY_DATA.get("show_dual_currency", True):
            # حساب المبلغ بالدولار
            usd_amount = amount / exchange_rate
            usd_formatted = f"${usd_amount:,.2f}"
            return f"{iqd_formatted} (≈ {usd_formatted})"
        else:
            return iqd_formatted
    except:
        return "0.00 د.ع"

def format_currency_iqd(amount):
    """تنسيق المبلغ بالدينار العراقي فقط"""
    try:
        amount = float(amount)
        return f"{amount:,.0f} د.ع"
    except:
        return "0.00 د.ع"

def format_currency_usd(amount):
    """تنسيق المبلغ بالدولار الأمريكي"""
    try:
        amount = float(amount)
        exchange_rate = CURRENCY_DATA.get("exchange_rate", 1500)
        usd_amount = amount / exchange_rate
        return f"${usd_amount:,.2f}"
    except:
        return "$0.00"

def get_exchange_rate():
    """الحصول على سعر الصرف الحالي"""
    return CURRENCY_DATA.get("exchange_rate", 1500)

def set_exchange_rate(rate):
    """تحديث سعر الصرف"""
    CURRENCY_DATA["exchange_rate"] = float(rate)
